/**
 * VSCode Augment Clear Controller
 * 基于outps.js分析的完整Augment数据清理控制器
 */

import * as vscode from 'vscode';
import * as path from 'path';
import * as os from 'os';
import { ResetOptions, ResetResult, ResetProgress, BackupInfo } from '../types';
import { DatabaseManager } from '../core/DatabaseManager';
import { HardwareManager } from '../core/HardwareManager';
import { NetworkManager } from '../core/NetworkManager';
import { FileSystemManager } from '../core/FileSystemManager';
import { BackupManager } from '../core/BackupManager';
import { DeviceManager } from '../managers/DeviceManager';
import { SessionManager } from '../managers/SessionManager';
import { StorageManager } from '../managers/StorageManager';
import { Logger } from '../utils/logger';
import { CryptoUtils } from '../utils/crypto';
import { RESET_STAGES, SUCCESS_MESSAGES, ERROR_MESSAGES, PROGRESS_WEIGHTS } from '../utils/constants';

export class ResetController {
    private context: vscode.ExtensionContext;
    private logger: Logger;

    // 核心管理器 - 基于outps.js分析
    private databaseManager: DatabaseManager;
    private hardwareManager: HardwareManager;
    private networkManager: NetworkManager;
    private fileSystemManager: FileSystemManager;
    private backupManager: BackupManager;

    // 传统管理器
    private deviceManager: DeviceManager;
    private sessionManager: SessionManager;
    private storageManager: StorageManager;

    private progressCallback?: (progress: ResetProgress) => void;
    private isInitialized: boolean = false;

    constructor(context: vscode.ExtensionContext) {
        this.context = context;
        this.logger = Logger.getInstance(context);

        // 初始化传统管理器
        this.deviceManager = new DeviceManager(context);
        this.sessionManager = new SessionManager(context);
        this.storageManager = new StorageManager(context);

        // 初始化核心管理器将在initialize()中完成
    }

    /**
     * 初始化所有管理器
     */
    async initialize(): Promise<void> {
        if (this.isInitialized) {
            return;
        }

        try {
            this.logger.info('Initializing VSCode Augment Clear controller');

            // 初始化硬件管理器
            this.hardwareManager = new HardwareManager(this.logger);

            // 初始化数据库管理器
            const dbConfig = {
                location: path.join(this.context.globalStorageUri?.fsPath || '', 'leveldb'),
                createIfMissing: true,
                errorIfExists: false,
                compression: true,
                cacheSize: 8 * 1024 * 1024, // 8MB
                writeBufferSize: 4 * 1024 * 1024, // 4MB
                blockSize: 4096,
                maxOpenFiles: 1000,
                blockRestartInterval: 16,
                maxFileSize: 2 * 1024 * 1024 // 2MB
            };
            this.databaseManager = new DatabaseManager(dbConfig, this.logger);

            // 初始化网络管理器
            const networkConfig = {
                apiBaseUrl: 'https://api.augment.com',
                timeout: 30000,
                retryAttempts: 3,
                retryDelay: 1000,
                maxConcurrentRequests: 10,
                userAgent: 'VSCode-Augment-Reset/1.0.0',
                enableCompression: true,
                enableKeepAlive: true,
                maxSockets: 50
            };
            this.networkManager = new NetworkManager(networkConfig, this.logger);

            // 初始化文件系统管理器
            const fsConfig = {
                basePath: this.context.globalStorageUri?.fsPath || '',
                tempPath: os.tmpdir(),
                backupPath: path.join(this.context.globalStorageUri?.fsPath || '', 'backups'),
                maxFileSize: 100 * 1024 * 1024, // 100MB
                allowedExtensions: ['.json', '.db', '.log', '.cache', '.tmp'],
                blockedPaths: ['/system', '/windows', '/program files'],
                secureDeletePasses: 7, // DoD 5220.22-M标准
                enableWatching: false,
                watchIgnorePatterns: ['node_modules', '.git', '.vscode']
            };
            this.fileSystemManager = new FileSystemManager(fsConfig, this.logger);

            // 初始化备份管理器
            const backupConfig = {
                backupPath: path.join(this.context.globalStorageUri?.fsPath || '', 'backups'),
                maxBackups: 10,
                compressionLevel: 9,
                encryptionEnabled: true,
                encryptionAlgorithm: 'aes-256-gcm',
                checksumAlgorithm: 'sha256',
                includePatterns: ['**/*'],
                excludePatterns: ['**/node_modules/**', '**/.git/**'],
                maxBackupSize: 1024 * 1024 * 1024, // 1GB
                retentionDays: 30
            };
            this.backupManager = new BackupManager(backupConfig, this.logger);

            // 初始化文件系统管理器
            await this.fileSystemManager.initialize();

            this.isInitialized = true;
            this.logger.info('VSCode Augment Clear controller initialized successfully');

        } catch (error) {
            this.logger.error('Failed to initialize reset controller', error);
            throw new Error(`Reset controller initialization failed: ${error}`);
        }
    }

    /**
     * 设置进度回调
     */
    setProgressCallback(callback: (progress: ResetProgress) => void): void {
        this.progressCallback = callback;
    }

    /**
     * 执行完整重置 - 基于outps.js的完整功能
     */
    async resetAll(options?: Partial<ResetOptions>): Promise<ResetResult> {
        // 确保已初始化
        await this.initialize();

        const defaultOptions: ResetOptions = {
            resetDevice: true,
            resetSession: true,
            resetStorage: true,
            resetConfig: true,
            resetCache: true,
            createBackup: true,
            secureDelete: true
        };

        const finalOptions = { ...defaultOptions, ...options };

        this.logger.info('Starting complete VSCode Augment data clearing', finalOptions);

        const startTime = Date.now();
        const result: ResetResult = {
            success: false,
            message: '',
            details: {
                deviceReset: false,
                sessionReset: false,
                storageReset: false,
                configReset: false,
                cacheReset: false,
                backupCreated: false
            },
            errors: [],
            warnings: [],
            duration: 0
        };

        try {
            // 阶段1: 初始化和备份
            this.updateProgress(RESET_STAGES.INITIALIZING, 0, 'Initializing production reset process...');

            if (finalOptions.createBackup) {
                this.updateProgress(RESET_STAGES.CREATING_BACKUP, 5, 'Creating encrypted backup...');
                try {
                    await this.createProductionBackup();
                    result.details.backupCreated = true;
                } catch (error) {
                    result.warnings.push(`Backup creation failed: ${error}`);
                    this.logger.warn('Backup creation failed', error);
                }
            }

            // 阶段2: 数据库完全销毁 - 基于outps.js的destroy_db
            this.updateProgress(RESET_STAGES.CLEARING_STORAGE, 15, 'Destroying LevelDB databases...');
            try {
                await this.destroyAllDatabases();
                result.details.storageReset = true;
            } catch (error) {
                result.errors.push(`Database destruction failed: ${error}`);
                this.logger.error('Database destruction failed', error);
            }

            // 阶段3: 硬件指纹重新生成
            this.updateProgress(RESET_STAGES.RESETTING_DEVICE, 25, 'Regenerating hardware fingerprints...');
            try {
                await this.regenerateHardwareFingerprints();
                result.details.deviceReset = true;
            } catch (error) {
                result.errors.push(`Hardware fingerprint reset failed: ${error}`);
                this.logger.error('Hardware fingerprint reset failed', error);
            }

            // 阶段4: 网络状态完全重置
            this.updateProgress(RESET_STAGES.RESETTING_SESSION, 35, 'Resetting network connections and auth...');
            try {
                await this.resetNetworkState();
                result.details.sessionReset = true;
            } catch (error) {
                result.errors.push(`Network reset failed: ${error}`);
                this.logger.error('Network reset failed', error);
            }

            // 阶段5: 文件系统完全清理
            this.updateProgress(RESET_STAGES.CLEARING_CACHE, 50, 'Performing complete file system cleanup...');
            try {
                await this.performCompleteFileSystemCleanup();
                result.details.cacheReset = true;
            } catch (error) {
                result.errors.push(`File system cleanup failed: ${error}`);
                this.logger.error('File system cleanup failed', error);
            }

            // 阶段6: VSCode扩展状态重置
            this.updateProgress(RESET_STAGES.RESETTING_CONFIG, 70, 'Resetting VSCode extension states...');
            try {
                await this.resetVSCodeExtensionStates();
                result.details.configReset = true;
            } catch (error) {
                result.errors.push(`VSCode state reset failed: ${error}`);
                this.logger.error('VSCode state reset failed', error);
            }

            // 阶段7: 传统管理器重置（兼容性）
            this.updateProgress(RESET_STAGES.VERIFYING, 85, 'Resetting traditional managers...');
            try {
                await this.resetTraditionalManagers(finalOptions);
            } catch (error) {
                result.warnings.push(`Traditional manager reset failed: ${error}`);
                this.logger.warn('Traditional manager reset failed', error);
            }

            // 阶段8: 验证重置结果
            this.updateProgress(RESET_STAGES.VERIFYING, 95, 'Verifying complete reset results...');
            await this.verifyCompleteResetResults(result);

            // 完成
            result.duration = Date.now() - startTime;
            result.success = result.errors.length === 0;
            result.message = result.success ? SUCCESS_MESSAGES.RESET_COMPLETE : ERROR_MESSAGES.RESET_FAILED;

            this.updateProgress(RESET_STAGES.COMPLETED, 100, result.message);
            this.logger.info('Production-grade machine code reset completed', result);

            return result;

        } catch (error) {
            result.duration = Date.now() - startTime;
            result.success = false;
            result.message = ERROR_MESSAGES.RESET_FAILED;
            result.errors.push(`Unexpected error: ${error}`);

            this.updateProgress(RESET_STAGES.FAILED, 0, `Reset failed: ${error}`);
            this.logger.error('Production-grade machine code reset failed', error);

            return result;
        }
    }

    /**
     * 重置设备标识符
     */
    async resetDevice(): Promise<ResetResult> {
        return this.resetAll({
            resetDevice: true,
            resetSession: false,
            resetStorage: false,
            resetConfig: false,
            resetCache: false,
            createBackup: true,
            secureDelete: false
        });
    }

    /**
     * 重置会话数据
     */
    async resetSession(): Promise<ResetResult> {
        return this.resetAll({
            resetDevice: false,
            resetSession: true,
            resetStorage: false,
            resetConfig: false,
            resetCache: false,
            createBackup: true,
            secureDelete: false
        });
    }

    /**
     * 重置存储数据
     */
    async resetStorage(): Promise<ResetResult> {
        return this.resetAll({
            resetDevice: false,
            resetSession: false,
            resetStorage: true,
            resetConfig: false,
            resetCache: false,
            createBackup: true,
            secureDelete: true
        });
    }

    /**
     * 创建生产级备份
     */
    private async createProductionBackup(): Promise<BackupInfo> {
        try {
            this.logger.info('Creating production-grade encrypted backup');

            // 收集所有需要备份的路径
            const sourcePaths = [
                this.context.globalStorageUri?.fsPath || '',
                this.context.storageUri?.fsPath || '',
                this.context.logUri?.fsPath || ''
            ].filter(Boolean);

            // 使用备份管理器创建加密备份
            const backupMetadata = await this.backupManager.createBackup(
                sourcePaths,
                `MachineReset_${Date.now()}`,
                'Complete machine code reset backup with encryption'
            );

            // 转换为BackupInfo格式
            const deviceInfo = await this.deviceManager.getCurrentDeviceInfo();
            const backupInfo: BackupInfo = {
                id: backupMetadata.id,
                name: backupMetadata.name,
                createdAt: backupMetadata.createdAt,
                size: backupMetadata.size,
                deviceInfo,
                dataTypes: ['device', 'session', 'storage', 'database', 'filesystem'],
                filePath: '' // 由备份管理器管理
            };

            this.logger.info('Production backup created successfully', backupInfo);
            return backupInfo;

        } catch (error) {
            this.logger.error('Failed to create production backup', error);
            throw error;
        }
    }

    /**
     * 销毁所有数据库 - 基于outps.js的destroy_db功能
     */
    private async destroyAllDatabases(): Promise<void> {
        try {
            this.logger.info('Starting complete database destruction');

            // 打开数据库连接
            await this.databaseManager.open();

            // 获取数据库统计信息（用于日志）
            const stats = await this.databaseManager.getStats();
            this.logger.info('Database stats before destruction', stats);

            // 清除所有数据
            const deletedCount = await this.databaseManager.clearAll();
            this.logger.info(`Cleared ${deletedCount} database entries`);

            // 关闭数据库连接
            await this.databaseManager.close();

            // 完全销毁数据库
            await this.databaseManager.destroyDatabase();

            // 验证销毁结果
            const statsAfter = await this.databaseManager.getStats().catch(() => null);
            if (statsAfter) {
                throw new Error('Database still exists after destruction');
            }

            this.logger.info('Database destruction completed successfully');

        } catch (error) {
            this.logger.error('Database destruction failed', error);
            throw new Error(`Database destruction failed: ${error}`);
        }
    }

    /**
     * 重新生成硬件指纹
     */
    private async regenerateHardwareFingerprints(): Promise<void> {
        try {
            this.logger.info('Regenerating hardware fingerprints');

            // 强制重新生成硬件指纹
            const newFingerprint = await this.hardwareManager.regenerateFingerprint();

            // 重置设备管理器中的设备标识符
            await this.deviceManager.resetDeviceIdentifiers();

            this.logger.info('Hardware fingerprints regenerated', {
                newFingerprint: newFingerprint.combinedFingerprint.substring(0, 16) + '...',
                cpuId: newFingerprint.cpuId,
                machineId: newFingerprint.machineId
            });

        } catch (error) {
            this.logger.error('Hardware fingerprint regeneration failed', error);
            throw error;
        }
    }

    /**
     * 重置网络状态
     */
    private async resetNetworkState(): Promise<void> {
        try {
            this.logger.info('Resetting complete network state');

            // 重置网络管理器状态
            await this.networkManager.resetNetworkState();

            // 重置会话管理器
            await this.sessionManager.resetSession();

            this.logger.info('Network state reset completed');

        } catch (error) {
            this.logger.error('Network state reset failed', error);
            throw error;
        }
    }

    /**
     * 执行完整文件系统清理
     */
    private async performCompleteFileSystemCleanup(): Promise<void> {
        try {
            this.logger.info('Starting complete file system cleanup');

            // 执行完整清理
            const cleanupResult = await this.fileSystemManager.performCompleteCleanup();

            this.logger.info('File system cleanup completed', {
                deletedFiles: cleanupResult.deletedFiles,
                deletedDirectories: cleanupResult.deletedDirectories,
                totalSize: cleanupResult.totalSize,
                duration: cleanupResult.duration,
                errors: cleanupResult.errors.length
            });

            if (cleanupResult.errors.length > 0) {
                this.logger.warn('File system cleanup had errors', cleanupResult.errors);
            }

        } catch (error) {
            this.logger.error('File system cleanup failed', error);
            throw error;
        }
    }

    /**
     * 重置VSCode扩展状态
     */
    private async resetVSCodeExtensionStates(): Promise<void> {
        try {
            this.logger.info('Resetting VSCode extension states');

            // 清除扩展相关的全局状态
            const extensionKeys = this.context.globalState.keys().filter(key =>
                key.includes('extension') ||
                key.includes('augment') ||
                key.includes('vscode') ||
                key.includes('workspace')
            );

            for (const key of extensionKeys) {
                await this.context.globalState.update(key, undefined);
            }

            // 清除工作空间状态
            if (this.context.workspaceState) {
                const workspaceKeys = this.context.workspaceState.keys();
                for (const key of workspaceKeys) {
                    await this.context.workspaceState.update(key, undefined);
                }
            }

            // 重置配置
            await this.resetConfiguration();

            this.logger.info('VSCode extension states reset completed');

        } catch (error) {
            this.logger.error('VSCode extension state reset failed', error);
            throw error;
        }
    }

    /**
     * 重置传统管理器（兼容性）
     */
    private async resetTraditionalManagers(options: ResetOptions): Promise<void> {
        try {
            this.logger.info('Resetting traditional managers for compatibility');

            // 重置存储管理器
            if (options.resetStorage) {
                await this.storageManager.resetStorage(options.secureDelete);
            }

            // 清除缓存
            if (options.resetCache) {
                await this.clearCache();
            }

            this.logger.info('Traditional managers reset completed');

        } catch (error) {
            this.logger.error('Traditional managers reset failed', error);
            throw error;
        }
    }

    /**
     * 清除缓存
     */
    private async clearCache(): Promise<void> {
        try {
            // 清除VSCode相关缓存
            const config = vscode.workspace.getConfiguration();
            
            // 这里可以添加更多缓存清理逻辑
            // 例如：清除扩展特定的缓存文件、临时文件等
            
            this.logger.info('Cache cleared successfully');

        } catch (error) {
            this.logger.error('Failed to clear cache', error);
            throw error;
        }
    }

    /**
     * 重置配置
     */
    private async resetConfiguration(): Promise<void> {
        try {
            const config = vscode.workspace.getConfiguration('machineCodeReset');
            
            // 重置插件配置到默认值
            await config.update('autoBackup', undefined, vscode.ConfigurationTarget.Global);
            await config.update('confirmReset', undefined, vscode.ConfigurationTarget.Global);
            await config.update('logLevel', undefined, vscode.ConfigurationTarget.Global);
            await config.update('secureDelete', undefined, vscode.ConfigurationTarget.Global);
            await config.update('backupRetention', undefined, vscode.ConfigurationTarget.Global);

            this.logger.info('Configuration reset successfully');

        } catch (error) {
            this.logger.error('Failed to reset configuration', error);
            throw error;
        }
    }

    /**
     * 验证完整重置结果
     */
    private async verifyCompleteResetResults(result: ResetResult): Promise<void> {
        try {
            this.logger.info('Starting complete reset verification');

            // 验证数据库是否已销毁
            if (result.details.storageReset) {
                try {
                    const stats = await this.databaseManager.getStats();
                    if (stats.keyCount > 0) {
                        result.warnings.push('Database still contains data after reset');
                    }
                } catch (error) {
                    // 数据库不存在是预期的结果
                    this.logger.debug('Database verification: database does not exist (expected)');
                }
            }

            // 验证硬件指纹是否已更新
            if (result.details.deviceReset) {
                try {
                    const fingerprint = await this.hardwareManager.getHardwareFingerprint();
                    const deviceInfo = await this.deviceManager.getCurrentDeviceInfo();

                    if (!deviceInfo.lastResetAt ||
                        deviceInfo.lastResetAt < new Date(Date.now() - 60000)) {
                        result.warnings.push('Device reset timestamp verification failed');
                    }

                    this.logger.debug('Hardware fingerprint verification completed', {
                        fingerprintId: fingerprint.combinedFingerprint.substring(0, 16) + '...'
                    });
                } catch (error) {
                    result.warnings.push(`Hardware fingerprint verification failed: ${error}`);
                }
            }

            // 验证网络状态是否已重置
            if (result.details.sessionReset) {
                try {
                    const networkStats = await this.networkManager.getNetworkStats();
                    const sessionStats = await this.sessionManager.getSessionStatistics();

                    if (sessionStats.authTokensCount > 0) {
                        result.warnings.push('Authentication tokens still present after reset');
                    }

                    if (networkStats.activeWebSockets > 0) {
                        result.warnings.push('Active WebSocket connections still present after reset');
                    }
                } catch (error) {
                    result.warnings.push(`Network state verification failed: ${error}`);
                }
            }

            // 验证文件系统清理
            if (result.details.cacheReset) {
                try {
                    const fsStats = await this.fileSystemManager.getDirectoryStats(
                        this.context.globalStorageUri?.fsPath || ''
                    );

                    this.logger.debug('File system verification completed', {
                        remainingFiles: fsStats.totalFiles,
                        remainingSize: fsStats.totalSize
                    });
                } catch (error) {
                    result.warnings.push(`File system verification failed: ${error}`);
                }
            }

            // 验证VSCode状态
            if (result.details.configReset) {
                const globalStateKeys = this.context.globalState.keys().length;
                const workspaceStateKeys = this.context.workspaceState?.keys().length || 0;

                if (globalStateKeys > 10) { // 允许一些基本键存在
                    result.warnings.push(`Too many global state keys remaining: ${globalStateKeys}`);
                }

                if (workspaceStateKeys > 0) {
                    result.warnings.push(`Workspace state keys still present: ${workspaceStateKeys}`);
                }
            }

            this.logger.info('Complete reset verification finished', {
                warningsCount: result.warnings.length,
                warnings: result.warnings
            });

        } catch (error) {
            result.warnings.push(`Complete verification failed: ${error}`);
            this.logger.error('Complete reset verification failed', error);
        }
    }

    /**
     * 验证重置结果（传统方法，保持兼容性）
     */
    private async verifyResetResults(result: ResetResult): Promise<void> {
        // 调用完整验证方法
        await this.verifyCompleteResetResults(result);
    }

    /**
     * 更新进度
     */
    private updateProgress(stage: string, progress: number, message: string): void {
        const progressInfo: ResetProgress = {
            stage,
            progress,
            message,
            currentOperation: message
        };

        this.logger.debug('Reset progress update', progressInfo);

        if (this.progressCallback) {
            this.progressCallback(progressInfo);
        }
    }

    /**
     * 获取完整重置状态
     */
    async getResetStatus(): Promise<any> {
        try {
            // 确保已初始化
            await this.initialize();

            this.logger.info('Collecting complete reset status');

            // 收集所有状态信息
            const [
                deviceStats,
                sessionStats,
                storageStats,
                hardwareInfo,
                networkStats,
                databaseStats,
                fileSystemStats
            ] = await Promise.allSettled([
                this.deviceManager.getDeviceStatistics(),
                this.sessionManager.getSessionStatistics(),
                this.storageManager.getStorageStatistics(),
                this.hardwareManager.getDetailedHardwareInfo(),
                this.networkManager.getNetworkStats(),
                this.getDatabaseStats(),
                this.getFileSystemStats()
            ]);

            return {
                device: this.getSettledValue(deviceStats),
                session: this.getSettledValue(sessionStats),
                storage: this.getSettledValue(storageStats),
                hardware: this.getSettledValue(hardwareInfo),
                network: this.getSettledValue(networkStats),
                database: this.getSettledValue(databaseStats),
                filesystem: this.getSettledValue(fileSystemStats),
                lastCheck: new Date(),
                isInitialized: this.isInitialized,
                version: '1.0.0-production'
            };

        } catch (error) {
            this.logger.error('Failed to get complete reset status', error);
            throw error;
        }
    }

    /**
     * 紧急重置模式
     */
    async emergencyReset(): Promise<ResetResult> {
        this.logger.warn('EMERGENCY RESET MODE ACTIVATED');

        return this.resetAll({
            resetDevice: true,
            resetSession: true,
            resetStorage: true,
            resetConfig: true,
            resetCache: true,
            createBackup: false, // 紧急模式跳过备份
            secureDelete: true
        });
    }

    /**
     * 获取数据库统计信息
     */
    private async getDatabaseStats(): Promise<any> {
        try {
            if (!this.isInitialized) {
                return { status: 'not_initialized' };
            }

            await this.databaseManager.open();
            const stats = await this.databaseManager.getStats();
            await this.databaseManager.close();

            return stats;
        } catch (error) {
            return { status: 'error', error: error.toString() };
        }
    }

    /**
     * 获取文件系统统计信息
     */
    private async getFileSystemStats(): Promise<any> {
        try {
            if (!this.isInitialized) {
                return { status: 'not_initialized' };
            }

            const basePath = this.context.globalStorageUri?.fsPath || '';
            const stats = await this.fileSystemManager.getDirectoryStats(basePath);

            return stats;
        } catch (error) {
            return { status: 'error', error: error.toString() };
        }
    }

    /**
     * 获取Promise结果值
     */
    private getSettledValue<T>(result: PromiseSettledResult<T>): T | null {
        return result.status === 'fulfilled' ? result.value : null;
    }

    /**
     * 销毁控制器
     */
    async destroy(): Promise<void> {
        try {
            this.logger.info('Destroying reset controller');

            if (this.isInitialized) {
                // 关闭所有管理器
                await Promise.allSettled([
                    this.databaseManager?.close(),
                    this.networkManager?.destroy(),
                    this.fileSystemManager ? Promise.resolve() : Promise.resolve()
                ]);
            }

            this.isInitialized = false;
            this.logger.info('Reset controller destroyed');

        } catch (error) {
            this.logger.error('Failed to destroy reset controller', error);
        }
    }
}
