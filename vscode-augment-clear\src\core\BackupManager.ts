/**
 * Production-Grade Backup Manager
 * 完整的备份、压缩、加密和恢复系统
 */

import * as fs from 'fs';
import * as path from 'path';
import * as crypto from 'crypto';
import * as archiver from 'archiver';
import * as yauzl from 'yauzl';
import { promisify } from 'util';
import { Logger } from '../utils/logger';
import { CryptoUtils } from '../utils/crypto';

const stat = promisify(fs.stat);
const readFile = promisify(fs.readFile);
const writeFile = promisify(fs.writeFile);
const mkdir = promisify(fs.mkdir);
const readdir = promisify(fs.readdir);
const unlink = promisify(fs.unlink);

export interface BackupConfig {
    backupPath: string;
    maxBackups: number;
    compressionLevel: number;
    encryptionEnabled: boolean;
    encryptionAlgorithm: string;
    checksumAlgorithm: string;
    includePatterns: string[];
    excludePatterns: string[];
    maxBackupSize: number;
    retentionDays: number;
}

export interface BackupMetadata {
    id: string;
    name: string;
    description: string;
    createdAt: Date;
    size: number;
    compressedSize: number;
    checksum: string;
    encrypted: boolean;
    version: string;
    sourceInfo: {
        hostname: string;
        platform: string;
        username: string;
        paths: string[];
    };
    compressionRatio: number;
    fileCount: number;
    directoryCount: number;
}

export interface BackupEntry {
    path: string;
    type: 'file' | 'directory';
    size: number;
    modified: Date;
    checksum?: string;
    encrypted: boolean;
}

export interface RestoreOptions {
    targetPath: string;
    overwriteExisting: boolean;
    preservePermissions: boolean;
    verifyChecksums: boolean;
    includePatterns?: string[];
    excludePatterns?: string[];
}

export interface BackupProgress {
    stage: 'scanning' | 'compressing' | 'encrypting' | 'finalizing';
    progress: number;
    currentFile: string;
    processedFiles: number;
    totalFiles: number;
    processedSize: number;
    totalSize: number;
    estimatedTimeRemaining: number;
}

export class BackupManager {
    private logger: Logger;
    private config: BackupConfig;
    private progressCallback?: (progress: BackupProgress) => void;

    constructor(config: BackupConfig, logger: Logger) {
        this.config = config;
        this.logger = logger;
    }

    /**
     * 设置进度回调
     */
    setProgressCallback(callback: (progress: BackupProgress) => void): void {
        this.progressCallback = callback;
    }

    /**
     * 创建完整备份
     */
    async createBackup(
        sourcePaths: string[],
        backupName: string,
        description: string = ''
    ): Promise<BackupMetadata> {
        const startTime = Date.now();
        
        try {
            this.logger.info('Starting backup creation', { backupName, sourcePaths });

            // 生成备份ID和路径
            const backupId = CryptoUtils.generateUUID();
            const backupFileName = `${backupName}_${Date.now()}.backup`;
            const backupFilePath = path.join(this.config.backupPath, backupFileName);
            const metadataPath = path.join(this.config.backupPath, `${backupId}.metadata.json`);

            // 确保备份目录存在
            await this.ensureBackupDirectory();

            // 扫描源文件
            this.updateProgress('scanning', 0, 'Scanning source files...');
            const entries = await this.scanSourceFiles(sourcePaths);
            
            const totalFiles = entries.filter(e => e.type === 'file').length;
            const totalSize = entries.reduce((sum, e) => sum + e.size, 0);

            this.logger.info('Source scan completed', { totalFiles, totalSize });

            // 创建压缩档案
            this.updateProgress('compressing', 0, 'Creating compressed archive...');
            const archiveInfo = await this.createCompressedArchive(entries, backupFilePath);

            // 加密备份文件（如果启用）
            let finalBackupPath = backupFilePath;
            if (this.config.encryptionEnabled) {
                this.updateProgress('encrypting', 80, 'Encrypting backup...');
                finalBackupPath = await this.encryptBackupFile(backupFilePath);
                await unlink(backupFilePath); // 删除未加密版本
            }

            // 计算校验和
            this.updateProgress('finalizing', 90, 'Calculating checksum...');
            const checksum = await this.calculateFileChecksum(finalBackupPath);

            // 获取最终文件大小
            const finalStats = await stat(finalBackupPath);

            // 创建备份元数据
            const metadata: BackupMetadata = {
                id: backupId,
                name: backupName,
                description,
                createdAt: new Date(),
                size: totalSize,
                compressedSize: finalStats.size,
                checksum,
                encrypted: this.config.encryptionEnabled,
                version: '1.0.0',
                sourceInfo: {
                    hostname: require('os').hostname(),
                    platform: require('os').platform(),
                    username: require('os').userInfo().username,
                    paths: sourcePaths
                },
                compressionRatio: totalSize > 0 ? archiveInfo.compressedSize / totalSize : 0,
                fileCount: totalFiles,
                directoryCount: entries.filter(e => e.type === 'directory').length
            };

            // 保存元数据
            await writeFile(metadataPath, JSON.stringify(metadata, null, 2));

            // 清理旧备份
            await this.cleanupOldBackups();

            this.updateProgress('finalizing', 100, 'Backup completed');

            const duration = Date.now() - startTime;
            this.logger.info('Backup created successfully', {
                backupId,
                duration,
                size: finalStats.size,
                compressionRatio: metadata.compressionRatio
            });

            return metadata;

        } catch (error) {
            this.logger.error('Backup creation failed', error);
            throw new Error(`Backup creation failed: ${error}`);
        }
    }

    /**
     * 恢复备份
     */
    async restoreBackup(
        backupId: string,
        options: RestoreOptions
    ): Promise<void> {
        try {
            this.logger.info('Starting backup restoration', { backupId, options });

            // 加载备份元数据
            const metadata = await this.loadBackupMetadata(backupId);
            if (!metadata) {
                throw new Error(`Backup metadata not found: ${backupId}`);
            }

            // 查找备份文件
            const backupFiles = await this.findBackupFiles(metadata.name, metadata.createdAt);
            if (backupFiles.length === 0) {
                throw new Error(`Backup file not found for: ${backupId}`);
            }

            const backupFilePath = backupFiles[0];

            // 验证备份文件完整性
            await this.verifyBackupIntegrity(backupFilePath, metadata);

            // 解密备份文件（如果需要）
            let workingFilePath = backupFilePath;
            if (metadata.encrypted) {
                this.logger.info('Decrypting backup file');
                workingFilePath = await this.decryptBackupFile(backupFilePath);
            }

            // 确保目标目录存在
            await mkdir(options.targetPath, { recursive: true });

            // 提取备份内容
            await this.extractBackupArchive(workingFilePath, options);

            // 清理临时文件
            if (workingFilePath !== backupFilePath) {
                await unlink(workingFilePath);
            }

            this.logger.info('Backup restoration completed successfully');

        } catch (error) {
            this.logger.error('Backup restoration failed', error);
            throw new Error(`Backup restoration failed: ${error}`);
        }
    }

    /**
     * 列出所有备份
     */
    async listBackups(): Promise<BackupMetadata[]> {
        try {
            const backups: BackupMetadata[] = [];
            const files = await readdir(this.config.backupPath);
            
            for (const file of files) {
                if (file.endsWith('.metadata.json')) {
                    try {
                        const metadataPath = path.join(this.config.backupPath, file);
                        const content = await readFile(metadataPath, 'utf8');
                        const metadata = JSON.parse(content) as BackupMetadata;
                        backups.push(metadata);
                    } catch (error) {
                        this.logger.warn(`Failed to load metadata: ${file}`, error);
                    }
                }
            }

            // 按创建时间排序
            backups.sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime());

            return backups;

        } catch (error) {
            this.logger.error('Failed to list backups', error);
            throw error;
        }
    }

    /**
     * 删除备份
     */
    async deleteBackup(backupId: string): Promise<void> {
        try {
            this.logger.info('Deleting backup', { backupId });

            // 加载元数据
            const metadata = await this.loadBackupMetadata(backupId);
            if (!metadata) {
                throw new Error(`Backup not found: ${backupId}`);
            }

            // 删除备份文件
            const backupFiles = await this.findBackupFiles(metadata.name, metadata.createdAt);
            for (const file of backupFiles) {
                await unlink(file);
            }

            // 删除元数据文件
            const metadataPath = path.join(this.config.backupPath, `${backupId}.metadata.json`);
            await unlink(metadataPath);

            this.logger.info('Backup deleted successfully', { backupId });

        } catch (error) {
            this.logger.error('Failed to delete backup', error);
            throw error;
        }
    }

    /**
     * 验证备份完整性
     */
    async verifyBackupIntegrity(backupFilePath: string, metadata: BackupMetadata): Promise<boolean> {
        try {
            this.logger.info('Verifying backup integrity', { backupFilePath });

            // 检查文件是否存在
            if (!await this.fileExists(backupFilePath)) {
                throw new Error('Backup file does not exist');
            }

            // 验证文件大小
            const stats = await stat(backupFilePath);
            if (stats.size !== metadata.compressedSize) {
                throw new Error(`File size mismatch: expected ${metadata.compressedSize}, got ${stats.size}`);
            }

            // 验证校验和
            const actualChecksum = await this.calculateFileChecksum(backupFilePath);
            if (actualChecksum !== metadata.checksum) {
                throw new Error(`Checksum mismatch: expected ${metadata.checksum}, got ${actualChecksum}`);
            }

            this.logger.info('Backup integrity verification passed');
            return true;

        } catch (error) {
            this.logger.error('Backup integrity verification failed', error);
            throw error;
        }
    }

    // 私有方法

    private async ensureBackupDirectory(): Promise<void> {
        try {
            await mkdir(this.config.backupPath, { recursive: true });
        } catch (error) {
            // 目录可能已存在
        }
    }

    private async scanSourceFiles(sourcePaths: string[]): Promise<BackupEntry[]> {
        const entries: BackupEntry[] = [];
        
        for (const sourcePath of sourcePaths) {
            await this.scanDirectory(sourcePath, entries);
        }

        return entries;
    }

    private async scanDirectory(dirPath: string, entries: BackupEntry[]): Promise<void> {
        try {
            const stats = await stat(dirPath);
            
            if (stats.isDirectory()) {
                entries.push({
                    path: dirPath,
                    type: 'directory',
                    size: 0,
                    modified: stats.mtime,
                    encrypted: false
                });

                const files = await readdir(dirPath);
                for (const file of files) {
                    const filePath = path.join(dirPath, file);
                    await this.scanDirectory(filePath, entries);
                }
            } else if (stats.isFile()) {
                entries.push({
                    path: dirPath,
                    type: 'file',
                    size: stats.size,
                    modified: stats.mtime,
                    encrypted: false
                });
            }
        } catch (error) {
            this.logger.warn(`Failed to scan: ${dirPath}`, error);
        }
    }

    private async createCompressedArchive(
        entries: BackupEntry[],
        outputPath: string
    ): Promise<{ compressedSize: number }> {
        return new Promise((resolve, reject) => {
            const output = fs.createWriteStream(outputPath);
            const archive = archiver('zip', {
                zlib: { level: this.config.compressionLevel }
            });

            let processedFiles = 0;
            const totalFiles = entries.filter(e => e.type === 'file').length;

            output.on('close', () => {
                resolve({ compressedSize: archive.pointer() });
            });

            archive.on('error', reject);
            archive.on('progress', (progress) => {
                const percent = (processedFiles / totalFiles) * 100;
                this.updateProgress('compressing', percent, `Processing file ${processedFiles}/${totalFiles}`);
            });

            archive.pipe(output);

            // 添加文件到档案
            for (const entry of entries) {
                if (entry.type === 'file') {
                    archive.file(entry.path, { name: path.relative(process.cwd(), entry.path) });
                    processedFiles++;
                }
            }

            archive.finalize();
        });
    }

    private async encryptBackupFile(filePath: string): Promise<string> {
        const encryptedPath = `${filePath}.encrypted`;
        const password = await this.generateEncryptionKey();
        
        const content = await readFile(filePath);
        const encrypted = CryptoUtils.encrypt(content.toString('base64'), password);
        
        await writeFile(encryptedPath, encrypted);
        return encryptedPath;
    }

    private async decryptBackupFile(filePath: string): Promise<string> {
        const decryptedPath = filePath.replace('.encrypted', '.decrypted');
        const password = await this.generateEncryptionKey();
        
        const encryptedContent = await readFile(filePath, 'utf8');
        const decrypted = CryptoUtils.decrypt(encryptedContent, password);
        const content = Buffer.from(decrypted, 'base64');
        
        await writeFile(decryptedPath, content);
        return decryptedPath;
    }

    private async generateEncryptionKey(): Promise<string> {
        // 在生产环境中，这应该从安全的密钥管理系统获取
        return 'backup-encryption-key-2024';
    }

    private async calculateFileChecksum(filePath: string): Promise<string> {
        const content = await readFile(filePath);
        return crypto.createHash(this.config.checksumAlgorithm).update(content).digest('hex');
    }

    private async loadBackupMetadata(backupId: string): Promise<BackupMetadata | null> {
        try {
            const metadataPath = path.join(this.config.backupPath, `${backupId}.metadata.json`);
            const content = await readFile(metadataPath, 'utf8');
            return JSON.parse(content) as BackupMetadata;
        } catch (error) {
            return null;
        }
    }

    private async findBackupFiles(backupName: string, createdAt: Date): Promise<string[]> {
        const files = await readdir(this.config.backupPath);
        const timestamp = createdAt.getTime();
        
        return files
            .filter(file => file.includes(backupName) && file.includes(timestamp.toString()))
            .map(file => path.join(this.config.backupPath, file));
    }

    private async extractBackupArchive(archivePath: string, options: RestoreOptions): Promise<void> {
        return new Promise((resolve, reject) => {
            yauzl.open(archivePath, { lazyEntries: true }, (err, zipfile) => {
                if (err) return reject(err);

                zipfile.readEntry();
                zipfile.on('entry', (entry) => {
                    const targetPath = path.join(options.targetPath, entry.fileName);
                    
                    if (/\/$/.test(entry.fileName)) {
                        // 目录
                        fs.mkdir(targetPath, { recursive: true }, (err) => {
                            if (err) return reject(err);
                            zipfile.readEntry();
                        });
                    } else {
                        // 文件
                        zipfile.openReadStream(entry, (err, readStream) => {
                            if (err) return reject(err);
                            
                            const writeStream = fs.createWriteStream(targetPath);
                            readStream.pipe(writeStream);
                            writeStream.on('close', () => zipfile.readEntry());
                        });
                    }
                });

                zipfile.on('end', resolve);
                zipfile.on('error', reject);
            });
        });
    }

    private async cleanupOldBackups(): Promise<void> {
        try {
            const backups = await this.listBackups();
            
            // 按数量清理
            if (backups.length > this.config.maxBackups) {
                const toDelete = backups.slice(this.config.maxBackups);
                for (const backup of toDelete) {
                    await this.deleteBackup(backup.id);
                }
            }

            // 按时间清理
            const cutoffDate = new Date();
            cutoffDate.setDate(cutoffDate.getDate() - this.config.retentionDays);
            
            const expiredBackups = backups.filter(b => b.createdAt < cutoffDate);
            for (const backup of expiredBackups) {
                await this.deleteBackup(backup.id);
            }

        } catch (error) {
            this.logger.warn('Failed to cleanup old backups', error);
        }
    }

    private async fileExists(filePath: string): Promise<boolean> {
        try {
            await stat(filePath);
            return true;
        } catch {
            return false;
        }
    }

    private updateProgress(
        stage: BackupProgress['stage'],
        progress: number,
        currentFile: string,
        processedFiles: number = 0,
        totalFiles: number = 0
    ): void {
        if (this.progressCallback) {
            this.progressCallback({
                stage,
                progress,
                currentFile,
                processedFiles,
                totalFiles,
                processedSize: 0,
                totalSize: 0,
                estimatedTimeRemaining: 0
            });
        }
    }
}
