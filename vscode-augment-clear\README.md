# VSCode Augment Clear - Production-Grade Data Clearing Extension

A **production-grade** VSCode extension for complete VSCode Augment data clearing and reset, based on comprehensive analysis of VSCode Augment's `outps.js` functionality. This extension provides enterprise-level device identifier clearing, LevelDB database destruction, network state reset, and complete file system cleanup specifically designed for VSCode Augment environments.

## 🏭 Production-Grade Features

### **Core Augment Clearing Functions** (Based on outps.js Analysis)
- **🗄️ Complete LevelDB Destruction** - Full Augment database destruction using `destroy_db` methodology
- **🔧 Hardware Fingerprint Regeneration** - Advanced hardware detection and Augment fingerprint regeneration
- **🌐 Network State Complete Reset** - Full Augment network connection and authentication state clearing
- **📁 File System Deep Cleanup** - Military-grade secure file deletion targeting Augment data (DoD 5220.22-M standard)
- **🔄 VSCode Augment State Reset** - Complete Augment extension state and workspace clearing
- **💾 Session & Auth Complete Clearing** - All Augment authentication tokens and session data removal

### **Enterprise Security Features**
- **🔐 AES-256-GCM Encryption** - Military-grade encryption for all backup operations
- **🗂️ 7-Pass Secure Deletion** - DoD 5220.22-M compliant secure file overwriting
- **🔍 Hardware-Based Fingerprinting** - CPU, motherboard, BIOS, disk serial-based identification
- **📊 Complete Audit Logging** - Comprehensive operation logging with integrity verification
- **🛡️ Multi-Layer Verification** - Complete verification of all reset operations

### **Production Infrastructure**
- **⚡ LevelDB Integration** - Native LevelDB operations for database management
- **🖥️ Cross-Platform Hardware Detection** - Windows, macOS, Linux hardware information gathering
- **🌐 WebSocket & HTTP Connection Management** - Complete network state management
- **📦 Compressed & Encrypted Backups** - Production-grade backup with compression and encryption
- **🔄 Atomic Operations** - All operations are atomic with rollback capability

### **Advanced User Experience**
- **📈 Real-Time Progress Tracking** - Detailed progress with stage-by-stage updates
- **🚨 Emergency Reset Mode** - Immediate reset without backup for critical situations
- **🔍 Complete System Analysis** - Detailed hardware and system state analysis
- **📋 Comprehensive Status Reports** - Full system state with hardware details
- **⚠️ Multi-Level Confirmations** - Safety confirmations for all destructive operations

## 📦 Installation

### From VSCode Marketplace
1. Open VSCode
2. Go to Extensions (Ctrl+Shift+X)
3. Search for "Machine Code Reset"
4. Click Install

### Manual Installation
1. Download the `.vsix` file from releases
2. Open VSCode
3. Run `Extensions: Install from VSIX...` command
4. Select the downloaded file

## 🎯 Usage

### Command Palette
Open Command Palette (Ctrl+Shift+P) and search for:

- `Machine Reset: Reset All Machine Codes` - Complete reset
- `Machine Reset: Reset Device Identifier` - Device ID only
- `Machine Reset: Reset Session Data` - Session data only
- `Machine Reset: Reset Local Storage` - Storage data only
- `Machine Reset: Show Reset Status` - View current status
- `Machine Reset: Create Backup` - Manual backup creation
- `Machine Reset: Restore Backup` - Restore from backup

### Context Menu
Right-click in Explorer to access Machine Code Reset submenu.

## ⚙️ Configuration

Configure the extension through VSCode settings:

```json
{
    "machineCodeReset.autoBackup": true,
    "machineCodeReset.confirmReset": true,
    "machineCodeReset.logLevel": "info",
    "machineCodeReset.secureDelete": true,
    "machineCodeReset.backupRetention": 5
}
```

### Configuration Options

| Setting | Type | Default | Description |
|---------|------|---------|-------------|
| `autoBackup` | boolean | `true` | Automatically create backup before reset |
| `confirmReset` | boolean | `true` | Show confirmation dialog before reset |
| `logLevel` | string | `"info"` | Logging level (debug, info, warn, error) |
| `secureDelete` | boolean | `true` | Use secure deletion methods |
| `backupRetention` | number | `5` | Number of backups to retain |

## 🔧 Technical Details

### Architecture
- **TypeScript** - Modern, type-safe development
- **Modular Design** - Separate managers for different data types
- **Event-Driven** - Progress callbacks and status updates
- **Cross-Platform** - Windows, macOS, and Linux support

### Data Types Reset
1. **Device Identifiers**
   - Device ID (UUID v4)
   - Machine ID (Hardware-based)
   - Session ID (Time-based)
   - Hardware Fingerprint (SHA256)

2. **Session Data**
   - Authentication tokens
   - User preferences
   - Recent files list
   - Workspace history

3. **Storage Data**
   - Global state
   - Workspace state
   - Secret storage
   - Extension data

### Security Measures
- **Multi-pass overwriting** for secure file deletion
- **Cryptographic hashing** for device fingerprinting
- **Secure random generation** for new identifiers
- **Memory clearing** after sensitive operations

## 🛡️ Security & Privacy

### Data Handling
- No data is transmitted over the network
- All operations are performed locally
- Sensitive data is securely overwritten
- Backups can be encrypted (optional)

### What Gets Reset
✅ Device identifiers and machine codes  
✅ Session tokens and authentication data  
✅ User preferences and settings  
✅ File access history  
✅ Workspace and project data  
✅ Extension-specific data  
✅ Cached files and temporary data  

### What Stays Safe
❌ Your source code files  
❌ Git repositories  
❌ System-wide settings  
❌ Other applications' data  

## 📊 Status Information

The extension provides detailed status information including:

- Current device identifiers
- Session statistics
- Storage usage
- Last reset timestamp
- Hardware information
- System details

## 🔄 Backup & Restore

### Automatic Backups
- Created before each reset operation
- Includes all resetable data
- Stored in extension storage directory
- Automatic cleanup of old backups

### Manual Backups
- Create backups on demand
- Custom naming and organization
- Verification of backup integrity
- Selective restore options

## 🐛 Troubleshooting

### Common Issues

**Reset operation fails**
- Check file permissions
- Ensure sufficient disk space
- Review logs for detailed error information

**Backup creation fails**
- Verify write permissions to storage directory
- Check available disk space
- Ensure no files are locked by other processes

**Hardware detection fails**
- Some hardware information may not be available
- Fallback identifiers will be generated
- Check logs for specific hardware detection errors

### Log Files
Logs are stored in the extension's global storage directory:
- Windows: `%APPDATA%\Code\User\globalStorage\machine-code-reset\logs\`
- macOS: `~/Library/Application Support/Code/User/globalStorage/machine-code-reset/logs/`
- Linux: `~/.config/Code/User/globalStorage/machine-code-reset/logs/`

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🔗 Links

- [GitHub Repository](https://github.com/your-username/machine-code-reset)
- [VSCode Marketplace](https://marketplace.visualstudio.com/items?itemName=machine-reset-tools.machine-code-reset)
- [Issue Tracker](https://github.com/your-username/machine-code-reset/issues)

## 📝 Changelog

### v1.0.0
- Initial release
- Complete machine code reset functionality
- Device identifier management
- Session and storage reset
- Backup and restore features
- Secure deletion support
- Cross-platform compatibility

---

**⚠️ Warning**: This extension performs destructive operations that cannot be undone. Always ensure you have backups of important data before using reset functions.
