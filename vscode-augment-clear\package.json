{"name": "vscode-augment-clear", "displayName": "VSCode Augment Clear", "description": "Production-grade VSCode Augment data clearing and reset utility based on comprehensive outps.js analysis.", "version": "1.0.0", "publisher": "augment-clear-tools", "engines": {"vscode": "^1.85.0", "node": ">=18.0.0"}, "categories": ["Other", "Debuggers", "Data Science"], "keywords": ["vscode augment", "augment clear", "data clearing", "privacy cleanup", "session reset", "leveldb clear", "hardware reset", "outps.js", "augment reset", "production clear"], "activationEvents": ["onStartupFinished"], "main": "./out/extension.js", "contributes": {"commands": [{"command": "machineCodeReset.resetAll", "title": "Clear All Augment Data (Complete)", "category": "Augment Clear", "icon": "$(refresh)"}, {"command": "machineCodeReset.resetDevice", "title": "Clear Device Identifiers", "category": "Augment Clear", "icon": "$(device-desktop)"}, {"command": "machineCodeReset.resetSession", "title": "Clear Session & Auth Data", "category": "Augment Clear", "icon": "$(sign-out)"}, {"command": "machineCodeReset.resetStorage", "title": "Clear LevelDB & Storage", "category": "Augment Clear", "icon": "$(database)"}, {"command": "augmentClear.clearWorkspace", "title": "Clear Workspace & File Tracking", "category": "Augment Clear", "icon": "$(folder)"}, {"command": "augmentClear.clearNetwork", "title": "Clear Network & API Connections", "category": "Augment Clear", "icon": "$(globe)"}, {"command": "augmentClear.showStatus", "title": "Show Complete Clear Status", "category": "Augment Clear", "icon": "$(info)"}, {"command": "augmentClear.showErrorLog", "title": "Show Augment Error Log", "category": "Augment Clear", "icon": "$(output)"}, {"command": "augmentClear.analyzeErrors", "title": "Analyze Augment Errors", "category": "Augment Clear", "icon": "$(graph)"}, {"command": "augmentClear.startErrorMonitoring", "title": "Start Error Monitoring", "category": "Augment Clear", "icon": "$(play)"}, {"command": "augmentClear.stopErrorMonitoring", "title": "Stop Error Monitoring", "category": "Augment Clear", "icon": "$(stop)"}, {"command": "augmentClear.testErrorHandling", "title": "Test Error Handling", "category": "Augment Clear", "icon": "$(beaker)"}, {"command": "augmentClear.clearErrorHistory", "title": "Clear Error History", "category": "Augment Clear", "icon": "$(clear-all)"}, {"command": "augmentClear.processErrorLog", "title": "Process Error Log Entry", "category": "Augment Clear", "icon": "$(code)"}, {"command": "machineCodeReset.createBackup", "title": "Create Production Backup", "category": "Augment Clear", "icon": "$(archive)"}, {"command": "machineCodeReset.restoreBackup", "title": "Restore Production Backup", "category": "Augment Clear", "icon": "$(history)"}, {"command": "machineCodeReset.analyzeSystem", "title": "Analyze System & Hardware", "category": "Augment Clear", "icon": "$(search)"}, {"command": "machineCodeReset.emergencyReset", "title": "Emergency Complete Clear", "category": "Augment Clear", "icon": "$(warning)"}], "menus": {"commandPalette": [{"command": "machineCodeReset.resetAll"}, {"command": "machineCodeReset.resetDevice"}, {"command": "machineCodeReset.resetSession"}, {"command": "machineCodeReset.resetStorage"}, {"command": "machineCodeReset.showStatus"}, {"command": "machineCodeReset.createBackup"}, {"command": "machineCodeReset.restoreBackup"}], "explorer/context": [{"submenu": "machineCodeReset.submenu", "group": "navigation"}]}, "submenus": [{"id": "machineCodeReset.submenu", "label": "Augment Clear", "icon": "$(refresh)"}], "configuration": {"title": "VSCode Augment Clear", "properties": {"machineCodeReset.autoBackup": {"type": "boolean", "default": true, "description": "Automatically create backup before reset operations"}, "machineCodeReset.confirmReset": {"type": "boolean", "default": true, "description": "Show confirmation dialog before reset operations"}, "machineCodeReset.logLevel": {"type": "string", "enum": ["debug", "info", "warn", "error"], "default": "info", "description": "Logging level for reset operations"}, "machineCodeReset.secureDelete": {"type": "boolean", "default": true, "description": "Use secure deletion methods for sensitive data"}, "machineCodeReset.backupRetention": {"type": "number", "default": 5, "description": "Number of backups to retain"}}}}, "scripts": {"vscode:prepublish": "npm run compile", "compile": "tsc -p ./", "watch": "tsc -watch -p ./"}, "devDependencies": {"@types/vscode": "^1.85.0", "@types/node": "^20.x", "typescript": "^5.3.0"}, "dependencies": {"uuid": "^9.0.1", "levelup": "^5.1.1", "leveldown": "^6.1.1", "abstract-leveldown": "^7.2.0", "encoding-down": "^7.1.0", "node-gyp": "^10.0.1", "systeminformation": "^5.21.20", "node-machine-id": "^1.1.12", "crypto-js": "^4.2.0", "archiver": "^6.0.1", "yauzl": "^2.10.0", "fast-glob": "^3.3.2", "chokidar": "^3.5.3", "ws": "^8.16.0", "axios": "^1.6.2", "jsonwebtoken": "^9.0.2", "bcryptjs": "^2.4.3", "node-forge": "^1.3.1", "sqlite3": "^5.1.6", "better-sqlite3": "^9.2.2"}, "os": ["win32", "darwin", "linux"], "cpu": ["x64", "arm64"]}