/**
 * Production-Grade Hardware Detection Manager
 * 基于outps.js分析的完整硬件检测和指纹生成
 */

import * as si from 'systeminformation';
import * as os from 'os';
import * as crypto from 'crypto';
import { exec } from 'child_process';
import { promisify } from 'util';
import { machineId } from 'node-machine-id';
import { Logger } from '../utils/logger';
import { CryptoUtils } from '../utils/crypto';

const execAsync = promisify(exec);

export interface HardwareFingerprint {
    cpuId: string;
    cpuSignature: string;
    motherboardSerial: string;
    motherboardUuid: string;
    biosSerial: string;
    biosVersion: string;
    diskSerial: string;
    diskUuid: string;
    networkMac: string[];
    memorySerial: string[];
    systemUuid: string;
    machineId: string;
    platformFingerprint: string;
    combinedFingerprint: string;
    timestamp: Date;
}

export interface SystemInfo {
    platform: string;
    arch: string;
    release: string;
    hostname: string;
    username: string;
    homedir: string;
    tmpdir: string;
    uptime: number;
    loadavg: number[];
    totalmem: number;
    freemem: number;
    cpus: os.CpuInfo[];
    networkInterfaces: NodeJS.Dict<os.NetworkInterfaceInfo[]>;
}

export interface DetailedHardwareInfo {
    cpu: any;
    system: any;
    baseboard: any;
    bios: any;
    memory: any;
    disk: any;
    network: any;
    graphics: any;
    audio: any;
    usb: any;
    printer: any;
    battery: any;
}

export class HardwareManager {
    private logger: Logger;
    private cachedFingerprint?: HardwareFingerprint;
    private cacheTimeout: number = 5 * 60 * 1000; // 5分钟缓存

    constructor(logger: Logger) {
        this.logger = logger;
    }

    /**
     * 获取完整的硬件指纹
     */
    async getHardwareFingerprint(forceRefresh: boolean = false): Promise<HardwareFingerprint> {
        try {
            // 检查缓存
            if (!forceRefresh && this.cachedFingerprint && 
                Date.now() - this.cachedFingerprint.timestamp.getTime() < this.cacheTimeout) {
                return this.cachedFingerprint;
            }

            this.logger.info('Generating hardware fingerprint');

            // 并行获取所有硬件信息
            const [
                cpuInfo,
                systemInfo,
                baseboardInfo,
                biosInfo,
                memoryInfo,
                diskInfo,
                networkInfo,
                nodeMachineId
            ] = await Promise.allSettled([
                this.getCpuInfo(),
                this.getSystemInfo(),
                this.getBaseboardInfo(),
                this.getBiosInfo(),
                this.getMemoryInfo(),
                this.getDiskInfo(),
                this.getNetworkInfo(),
                this.getNodeMachineId()
            ]);

            // 提取硬件信息
            const cpu = this.getSettledValue(cpuInfo);
            const system = this.getSettledValue(systemInfo);
            const baseboard = this.getSettledValue(baseboardInfo);
            const bios = this.getSettledValue(biosInfo);
            const memory = this.getSettledValue(memoryInfo);
            const disk = this.getSettledValue(diskInfo);
            const network = this.getSettledValue(networkInfo);
            const machineIdValue = this.getSettledValue(nodeMachineId);

            // 生成硬件指纹
            const fingerprint: HardwareFingerprint = {
                cpuId: this.extractCpuId(cpu),
                cpuSignature: this.extractCpuSignature(cpu),
                motherboardSerial: this.extractMotherboardSerial(baseboard),
                motherboardUuid: this.extractMotherboardUuid(baseboard),
                biosSerial: this.extractBiosSerial(bios),
                biosVersion: this.extractBiosVersion(bios),
                diskSerial: this.extractDiskSerial(disk),
                diskUuid: this.extractDiskUuid(disk),
                networkMac: this.extractNetworkMacs(network),
                memorySerial: this.extractMemorySerials(memory),
                systemUuid: this.extractSystemUuid(system),
                machineId: machineIdValue || '',
                platformFingerprint: this.generatePlatformFingerprint(),
                combinedFingerprint: '',
                timestamp: new Date()
            };

            // 生成组合指纹
            fingerprint.combinedFingerprint = this.generateCombinedFingerprint(fingerprint);

            // 缓存结果
            this.cachedFingerprint = fingerprint;

            this.logger.info('Hardware fingerprint generated successfully', {
                cpuId: fingerprint.cpuId,
                machineId: fingerprint.machineId,
                combinedFingerprint: fingerprint.combinedFingerprint.substring(0, 16) + '...'
            });

            return fingerprint;

        } catch (error) {
            this.logger.error('Failed to generate hardware fingerprint', error);
            throw new Error(`Hardware fingerprint generation failed: ${error}`);
        }
    }

    /**
     * 获取详细的硬件信息
     */
    async getDetailedHardwareInfo(): Promise<DetailedHardwareInfo> {
        try {
            this.logger.info('Collecting detailed hardware information');

            const [
                cpu,
                system,
                baseboard,
                bios,
                memory,
                disk,
                network,
                graphics,
                audio,
                usb,
                printer,
                battery
            ] = await Promise.allSettled([
                si.cpu(),
                si.system(),
                si.baseboard(),
                si.bios(),
                si.mem(),
                si.diskLayout(),
                si.networkInterfaces(),
                si.graphics(),
                si.audio(),
                si.usb(),
                si.printer(),
                si.battery()
            ]);

            return {
                cpu: this.getSettledValue(cpu),
                system: this.getSettledValue(system),
                baseboard: this.getSettledValue(baseboard),
                bios: this.getSettledValue(bios),
                memory: this.getSettledValue(memory),
                disk: this.getSettledValue(disk),
                network: this.getSettledValue(network),
                graphics: this.getSettledValue(graphics),
                audio: this.getSettledValue(audio),
                usb: this.getSettledValue(usb),
                printer: this.getSettledValue(printer),
                battery: this.getSettledValue(battery)
            };

        } catch (error) {
            this.logger.error('Failed to get detailed hardware info', error);
            throw error;
        }
    }

    /**
     * 获取系统信息
     */
    async getSystemInfo(): Promise<SystemInfo> {
        return {
            platform: os.platform(),
            arch: os.arch(),
            release: os.release(),
            hostname: os.hostname(),
            username: os.userInfo().username,
            homedir: os.homedir(),
            tmpdir: os.tmpdir(),
            uptime: os.uptime(),
            loadavg: os.loadavg(),
            totalmem: os.totalmem(),
            freemem: os.freemem(),
            cpus: os.cpus(),
            networkInterfaces: os.networkInterfaces()
        };
    }

    /**
     * 重新生成硬件指纹
     */
    async regenerateFingerprint(): Promise<HardwareFingerprint> {
        this.cachedFingerprint = undefined;
        return this.getHardwareFingerprint(true);
    }

    /**
     * 验证硬件指纹
     */
    async verifyFingerprint(expectedFingerprint: string): Promise<boolean> {
        try {
            const currentFingerprint = await this.getHardwareFingerprint();
            return currentFingerprint.combinedFingerprint === expectedFingerprint;
        } catch (error) {
            this.logger.error('Failed to verify fingerprint', error);
            return false;
        }
    }

    // 私有方法 - 硬件信息获取

    private async getCpuInfo(): Promise<any> {
        return si.cpu();
    }

    private async getSystemInfo(): Promise<any> {
        return si.system();
    }

    private async getBaseboardInfo(): Promise<any> {
        return si.baseboard();
    }

    private async getBiosInfo(): Promise<any> {
        return si.bios();
    }

    private async getMemoryInfo(): Promise<any> {
        return si.memLayout();
    }

    private async getDiskInfo(): Promise<any> {
        return si.diskLayout();
    }

    private async getNetworkInfo(): Promise<any> {
        return si.networkInterfaces();
    }

    private async getNodeMachineId(): Promise<string> {
        try {
            return await machineId();
        } catch (error) {
            this.logger.warn('Failed to get node machine ID', error);
            return '';
        }
    }

    // 私有方法 - 信息提取

    private extractCpuId(cpu: any): string {
        if (!cpu) return this.generateFallbackId('CPU');
        return cpu.processorId || cpu.id || cpu.serial || this.generateFallbackId('CPU');
    }

    private extractCpuSignature(cpu: any): string {
        if (!cpu) return '';
        const signature = [
            cpu.manufacturer,
            cpu.brand,
            cpu.family,
            cpu.model,
            cpu.stepping,
            cpu.cores,
            cpu.physicalCores
        ].filter(Boolean).join('|');
        return CryptoUtils.createHash(signature);
    }

    private extractMotherboardSerial(baseboard: any): string {
        if (!baseboard) return this.generateFallbackId('MB');
        return baseboard.serial || baseboard.serialNum || this.generateFallbackId('MB');
    }

    private extractMotherboardUuid(baseboard: any): string {
        if (!baseboard) return '';
        return baseboard.uuid || '';
    }

    private extractBiosSerial(bios: any): string {
        if (!bios) return this.generateFallbackId('BIOS');
        return bios.serial || bios.serialNumber || this.generateFallbackId('BIOS');
    }

    private extractBiosVersion(bios: any): string {
        if (!bios) return '';
        return bios.version || bios.revision || '';
    }

    private extractDiskSerial(disk: any): string {
        if (!disk || !Array.isArray(disk) || disk.length === 0) {
            return this.generateFallbackId('DISK');
        }
        const primaryDisk = disk[0];
        return primaryDisk.serialNum || primaryDisk.serial || this.generateFallbackId('DISK');
    }

    private extractDiskUuid(disk: any): string {
        if (!disk || !Array.isArray(disk) || disk.length === 0) return '';
        const primaryDisk = disk[0];
        return primaryDisk.uuid || '';
    }

    private extractNetworkMacs(network: any): string[] {
        if (!network || !Array.isArray(network)) return [];
        return network
            .filter(iface => iface.mac && iface.mac !== '00:00:00:00:00:00')
            .map(iface => iface.mac)
            .filter((mac, index, arr) => arr.indexOf(mac) === index); // 去重
    }

    private extractMemorySerials(memory: any): string[] {
        if (!memory || !Array.isArray(memory)) return [];
        return memory
            .filter(mem => mem.serialNum)
            .map(mem => mem.serialNum);
    }

    private extractSystemUuid(system: any): string {
        if (!system) return '';
        return system.uuid || system.serial || '';
    }

    private generatePlatformFingerprint(): string {
        const platformInfo = [
            os.platform(),
            os.arch(),
            os.release(),
            os.hostname(),
            os.cpus().length.toString(),
            os.totalmem().toString()
        ].join('|');
        return CryptoUtils.createHash(platformInfo);
    }

    private generateCombinedFingerprint(fingerprint: HardwareFingerprint): string {
        const components = [
            fingerprint.cpuId,
            fingerprint.cpuSignature,
            fingerprint.motherboardSerial,
            fingerprint.biosSerial,
            fingerprint.diskSerial,
            fingerprint.networkMac.join(','),
            fingerprint.systemUuid,
            fingerprint.machineId,
            fingerprint.platformFingerprint
        ].filter(Boolean).join('|');

        return CryptoUtils.createHash(components);
    }

    private generateFallbackId(prefix: string): string {
        const timestamp = Date.now().toString();
        const random = crypto.randomBytes(8).toString('hex');
        const hostname = os.hostname();
        const combined = `${prefix}-${hostname}-${timestamp}-${random}`;
        return CryptoUtils.createHash(combined).substring(0, 16).toUpperCase();
    }

    private getSettledValue<T>(result: PromiseSettledResult<T>): T | null {
        return result.status === 'fulfilled' ? result.value : null;
    }
}
