/**
 * VSCode Augment Clear Plugin Constants
 * 所有常量定义
 */

export const PLUGIN_ID = 'vscode-augment-clear';
export const PLUGIN_NAME = 'VSCode Augment Clear';
export const PLUGIN_VERSION = '1.0.0';

// 命令常量
export const COMMANDS = {
    CLEAR_ALL: 'augmentClear.clearAll',
    CLEAR_DEVICE: 'augmentClear.clearDevice',
    CLEAR_SESSION: 'augmentClear.clearSession',
    CLEAR_STORAGE: 'augmentClear.clearStorage',
    CLEAR_WORKSPACE: 'augmentClear.clearWorkspace',
    CLEAR_NETWORK: 'augmentClear.clearNetwork',
    SHOW_STATUS: 'augmentClear.showStatus',
    CREATE_BACKUP: 'augmentClear.createBackup',
    RESTORE_BACKUP: 'augmentClear.restoreBackup',
    ANALYZE_SYSTEM: 'augmentClear.analyzeSystem',
    EMERGENCY_CLEAR: 'augmentClear.emergencyReset',
    SHOW_ERROR_LOG: 'augmentClear.showErrorLog',
    ANALYZE_ERRORS: 'augmentClear.analyzeErrors'
} as const;

// 存储键常量
export const STORAGE_KEYS = {
    DEVICE_INFO: 'deviceInfo',
    SESSION_DATA: 'sessionData',
    RESET_HISTORY: 'resetHistory',
    BACKUP_LIST: 'backupList',
    STATISTICS: 'statistics',
    LAST_RESET: 'lastReset',
    MACHINE_ID: 'machineId',
    HARDWARE_FINGERPRINT: 'hardwareFingerprint'
} as const;

// 文件路径常量
export const PATHS = {
    BACKUP_DIR: 'augment-clear-backups',
    LOG_DIR: 'augment-clear-logs',
    CACHE_DIR: 'augment-clear-cache',
    DATA_DIR: 'data',
    ERROR_LOG: 'data/erro.log',
    ERROR_STATS: 'data/error-stats.json',
    CONFIG_FILE: 'augment-clear-config.json',
    DEVICE_FILE: 'device-info.json',
    SESSION_FILE: 'session-data.json'
} as const;

// 清理阶段常量
export const RESET_STAGES = {
    INITIALIZING: 'initializing',
    CREATING_BACKUP: 'creating_backup',
    CLEARING_DEVICE: 'clearing_device',
    CLEARING_SESSION: 'clearing_session',
    CLEARING_STORAGE: 'clearing_storage',
    CLEARING_CACHE: 'clearing_cache',
    CLEARING_CONFIG: 'clearing_config',
    CLEARING_NETWORK: 'clearing_network',
    CLEARING_WORKSPACE: 'clearing_workspace',
    VERIFYING: 'verifying',
    COMPLETED: 'completed',
    FAILED: 'failed'
} as const;

// 错误管理常量
export const ERROR_CONFIG = {
    MAX_LOG_FILE_SIZE: 10 * 1024 * 1024, // 10MB
    MAX_LOG_FILES: 5,
    MAX_ERROR_HISTORY: 1000,
    AUTO_RECOVERY_ENABLED: true,
    MAX_RETRY_ATTEMPTS: 3,
    RETRY_DELAY: 1000,
    ERROR_NOTIFICATION_THRESHOLD: 'HIGH',
    PATTERN_DETECTION_ENABLED: true
} as const;

// 错误类型映射
export const ERROR_TYPE_MAPPING = {
    'ENOENT': 'FS_001',
    'EACCES': 'FS_002',
    'ENOSPC': 'FS_003',
    'ETIMEDOUT': 'NET_001',
    'ECONNREFUSED': 'NET_004',
    'ENOTFOUND': 'NET_004'
} as const;

// 日志级别常量
export const LOG_LEVELS = {
    DEBUG: 'debug',
    INFO: 'info',
    WARN: 'warn',
    ERROR: 'error'
} as const;

// 数据类型常量
export const DATA_TYPES = {
    DEVICE: 'device',
    SESSION: 'session',
    STORAGE: 'storage',
    CACHE: 'cache',
    CONFIG: 'config',
    SECRETS: 'secrets',
    PREFERENCES: 'preferences'
} as const;

// 默认配置
export const DEFAULT_CONFIG = {
    autoBackup: true,
    confirmReset: true,
    logLevel: 'info',
    secureDelete: true,
    backupRetention: 5,
    maxBackupSize: 100 * 1024 * 1024, // 100MB
    encryptBackups: false,
    overwritePasses: 3
} as const;

// 安全删除配置
export const SECURITY_CONFIG = {
    DEFAULT_OVERWRITE_PASSES: 3,
    MAX_OVERWRITE_PASSES: 10,
    RANDOM_DATA_SIZE: 1024,
    VERIFICATION_ATTEMPTS: 3
} as const;

// 硬件信息获取命令
export const HARDWARE_COMMANDS = {
    CPU_ID: {
        windows: 'wmic cpu get ProcessorId /value',
        linux: 'cat /proc/cpuinfo | grep "processor" | head -1',
        darwin: 'sysctl -n machdep.cpu.brand_string'
    },
    MOTHERBOARD_ID: {
        windows: 'wmic baseboard get SerialNumber /value',
        linux: 'sudo dmidecode -s baseboard-serial-number',
        darwin: 'system_profiler SPHardwareDataType | grep "Serial Number"'
    },
    DISK_ID: {
        windows: 'wmic diskdrive get SerialNumber /value',
        linux: 'lsblk -o NAME,SERIAL',
        darwin: 'diskutil info disk0 | grep "Device / Media Name"'
    },
    NETWORK_MAC: {
        windows: 'getmac /v /fo csv',
        linux: 'cat /sys/class/net/*/address',
        darwin: 'ifconfig en0 | grep ether'
    },
    BIOS_ID: {
        windows: 'wmic bios get SerialNumber /value',
        linux: 'sudo dmidecode -s bios-version',
        darwin: 'system_profiler SPHardwareDataType | grep "Boot ROM Version"'
    },
    SYSTEM_UUID: {
        windows: 'wmic csproduct get UUID /value',
        linux: 'sudo dmidecode -s system-uuid',
        darwin: 'system_profiler SPHardwareDataType | grep "Hardware UUID"'
    }
} as const;

// 错误消息
export const ERROR_MESSAGES = {
    RESET_FAILED: 'Reset operation failed',
    BACKUP_FAILED: 'Backup creation failed',
    RESTORE_FAILED: 'Backup restoration failed',
    INVALID_CONFIG: 'Invalid configuration',
    PERMISSION_DENIED: 'Permission denied',
    FILE_NOT_FOUND: 'File not found',
    DISK_FULL: 'Insufficient disk space',
    NETWORK_ERROR: 'Network error occurred',
    TIMEOUT: 'Operation timed out',
    UNKNOWN_ERROR: 'Unknown error occurred'
} as const;

// 成功消息
export const SUCCESS_MESSAGES = {
    RESET_COMPLETE: 'Machine code reset completed successfully',
    BACKUP_CREATED: 'Backup created successfully',
    BACKUP_RESTORED: 'Backup restored successfully',
    DEVICE_RESET: 'Device identifier reset successfully',
    SESSION_RESET: 'Session data reset successfully',
    STORAGE_CLEARED: 'Storage cleared successfully',
    CACHE_CLEARED: 'Cache cleared successfully',
    CONFIG_RESET: 'Configuration reset successfully'
} as const;

// 文件扩展名
export const FILE_EXTENSIONS = {
    BACKUP: '.mcr-backup',
    LOG: '.log',
    CONFIG: '.json',
    ENCRYPTED: '.encrypted'
} as const;

// 超时设置（毫秒）
export const TIMEOUTS = {
    RESET_OPERATION: 30000,
    BACKUP_OPERATION: 60000,
    HARDWARE_SCAN: 10000,
    FILE_OPERATION: 5000
} as const;

// 进度阶段权重
export const PROGRESS_WEIGHTS = {
    [RESET_STAGES.INITIALIZING]: 5,
    [RESET_STAGES.CREATING_BACKUP]: 15,
    [RESET_STAGES.CLEARING_DEVICE]: 10,
    [RESET_STAGES.CLEARING_SESSION]: 10,
    [RESET_STAGES.CLEARING_STORAGE]: 20,
    [RESET_STAGES.CLEARING_CACHE]: 15,
    [RESET_STAGES.CLEARING_CONFIG]: 10,
    [RESET_STAGES.CLEARING_NETWORK]: 5,
    [RESET_STAGES.CLEARING_WORKSPACE]: 5,
    [RESET_STAGES.VERIFYING]: 5
} as const;

// 正则表达式
export const REGEX_PATTERNS = {
    UUID: /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i,
    MAC_ADDRESS: /^([0-9A-Fa-f]{2}[:-]){5}([0-9A-Fa-f]{2})$/,
    SERIAL_NUMBER: /^[A-Z0-9\-_]{6,}$/i,
    DEVICE_ID: /^[A-Z0-9]{8,32}$/i
} as const;
