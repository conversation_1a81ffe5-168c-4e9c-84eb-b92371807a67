/**
 * Production-Grade LevelDB Database Manager
 * 基于outps.js分析的完整数据库重置功能
 */

import * as levelup from 'levelup';
import * as leveldown from 'leveldown';
import * as encoding from 'encoding-down';
import * as fs from 'fs';
import * as path from 'path';
import { promisify } from 'util';
import { Logger } from '../utils/logger';
import { CryptoUtils } from '../utils/crypto';

export interface DatabaseConfig {
    location: string;
    createIfMissing: boolean;
    errorIfExists: boolean;
    compression: boolean;
    cacheSize: number;
    writeBufferSize: number;
    blockSize: number;
    maxOpenFiles: number;
    blockRestartInterval: number;
    maxFileSize: number;
}

export interface DatabaseStats {
    location: string;
    size: number;
    keyCount: number;
    approximateSize: number;
    compactionStats: any;
    properties: any;
}

export class DatabaseManager {
    private db: any;
    private logger: Logger;
    private config: DatabaseConfig;
    private isOpen: boolean = false;
    private operationQueue: Array<() => Promise<any>> = [];
    private isProcessingQueue: boolean = false;

    constructor(config: DatabaseConfig, logger: Logger) {
        this.config = config;
        this.logger = logger;
    }

    /**
     * 打开数据库连接
     */
    async open(): Promise<void> {
        try {
            if (this.isOpen) {
                this.logger.warn('Database is already open');
                return;
            }

            // 确保目录存在
            await this.ensureDirectory(this.config.location);

            // 创建LevelDB实例
            const leveldownInstance = leveldown(this.config.location);
            const encodedDb = encoding(leveldownInstance, { valueEncoding: 'json' });
            
            this.db = levelup(encodedDb, {
                createIfMissing: this.config.createIfMissing,
                errorIfExists: this.config.errorIfExists,
                compression: this.config.compression,
                cacheSize: this.config.cacheSize,
                writeBufferSize: this.config.writeBufferSize,
                blockSize: this.config.blockSize,
                maxOpenFiles: this.config.maxOpenFiles,
                blockRestartInterval: this.config.blockRestartInterval,
                maxFileSize: this.config.maxFileSize
            });

            // 等待数据库打开
            await new Promise((resolve, reject) => {
                this.db.on('open', resolve);
                this.db.on('error', reject);
            });

            this.isOpen = true;
            this.logger.info('Database opened successfully', { location: this.config.location });

        } catch (error) {
            this.logger.error('Failed to open database', error);
            throw new Error(`Database open failed: ${error}`);
        }
    }

    /**
     * 关闭数据库连接
     */
    async close(): Promise<void> {
        try {
            if (!this.isOpen || !this.db) {
                return;
            }

            // 等待队列中的操作完成
            await this.waitForQueueCompletion();

            // 关闭数据库
            await new Promise((resolve, reject) => {
                this.db.close((error: any) => {
                    if (error) {
                        reject(error);
                    } else {
                        resolve(undefined);
                    }
                });
            });

            this.isOpen = false;
            this.db = null;
            this.logger.info('Database closed successfully');

        } catch (error) {
            this.logger.error('Failed to close database', error);
            throw error;
        }
    }

    /**
     * 完全销毁数据库 - 基于outps.js的destroy_db功能
     */
    async destroyDatabase(): Promise<void> {
        try {
            this.logger.info('Starting database destruction', { location: this.config.location });

            // 关闭数据库连接
            if (this.isOpen) {
                await this.close();
            }

            // 使用LevelDB的原生销毁功能
            await new Promise((resolve, reject) => {
                leveldown.destroy(this.config.location, (error: any) => {
                    if (error) {
                        reject(error);
                    } else {
                        resolve(undefined);
                    }
                });
            });

            // 验证销毁结果
            const exists = await this.databaseExists();
            if (exists) {
                throw new Error('Database still exists after destruction');
            }

            this.logger.info('Database destroyed successfully');

        } catch (error) {
            this.logger.error('Failed to destroy database', error);
            throw new Error(`Database destruction failed: ${error}`);
        }
    }

    /**
     * 修复损坏的数据库 - 基于outps.js的repair_db功能
     */
    async repairDatabase(): Promise<void> {
        try {
            this.logger.info('Starting database repair', { location: this.config.location });

            // 关闭数据库连接
            if (this.isOpen) {
                await this.close();
            }

            // 使用LevelDB的原生修复功能
            await new Promise((resolve, reject) => {
                leveldown.repair(this.config.location, (error: any) => {
                    if (error) {
                        reject(error);
                    } else {
                        resolve(undefined);
                    }
                });
            });

            this.logger.info('Database repaired successfully');

        } catch (error) {
            this.logger.error('Failed to repair database', error);
            throw new Error(`Database repair failed: ${error}`);
        }
    }

    /**
     * 清除数据库范围数据 - 基于outps.js的clear功能
     */
    async clearRange(options: {
        gt?: string;
        gte?: string;
        lt?: string;
        lte?: string;
        limit?: number;
        reverse?: boolean;
    } = {}): Promise<number> {
        try {
            if (!this.isOpen) {
                throw new Error('Database is not open');
            }

            this.logger.info('Starting range clear operation', options);

            let deletedCount = 0;
            const batch = this.db.batch();

            // 创建迭代器
            const iterator = this.db.iterator({
                gt: options.gt,
                gte: options.gte,
                lt: options.lt,
                lte: options.lte,
                limit: options.limit,
                reverse: options.reverse,
                keys: true,
                values: false
            });

            // 收集要删除的键
            const keysToDelete: string[] = [];
            
            await new Promise((resolve, reject) => {
                const processNext = () => {
                    iterator.next((error: any, key: string, value: any) => {
                        if (error) {
                            return reject(error);
                        }

                        if (key === undefined) {
                            // 迭代完成
                            return resolve(undefined);
                        }

                        keysToDelete.push(key);
                        processNext();
                    });
                };

                processNext();
            });

            // 批量删除
            for (const key of keysToDelete) {
                batch.del(key);
                deletedCount++;
            }

            // 提交批量操作
            await new Promise((resolve, reject) => {
                batch.write((error: any) => {
                    if (error) {
                        reject(error);
                    } else {
                        resolve(undefined);
                    }
                });
            });

            // 关闭迭代器
            await new Promise((resolve, reject) => {
                iterator.end((error: any) => {
                    if (error) {
                        reject(error);
                    } else {
                        resolve(undefined);
                    }
                });
            });

            this.logger.info('Range clear completed', { deletedCount });
            return deletedCount;

        } catch (error) {
            this.logger.error('Failed to clear range', error);
            throw new Error(`Range clear failed: ${error}`);
        }
    }

    /**
     * 清除所有数据
     */
    async clearAll(): Promise<number> {
        return this.clearRange({});
    }

    /**
     * 获取数据库统计信息
     */
    async getStats(): Promise<DatabaseStats> {
        try {
            if (!this.isOpen) {
                throw new Error('Database is not open');
            }

            // 获取数据库大小
            const size = await this.getDatabaseSize();
            
            // 计算键数量
            const keyCount = await this.getKeyCount();

            // 获取近似大小
            const approximateSize = await this.getApproximateSize();

            // 获取数据库属性
            const properties = await this.getDatabaseProperties();

            return {
                location: this.config.location,
                size,
                keyCount,
                approximateSize,
                compactionStats: {},
                properties
            };

        } catch (error) {
            this.logger.error('Failed to get database stats', error);
            throw error;
        }
    }

    /**
     * 压缩数据库
     */
    async compactDatabase(): Promise<void> {
        try {
            if (!this.isOpen) {
                throw new Error('Database is not open');
            }

            this.logger.info('Starting database compaction');

            // LevelDB自动压缩，这里触发手动压缩
            await new Promise((resolve, reject) => {
                // 通过读取所有数据来触发压缩
                const iterator = this.db.iterator();
                const processNext = () => {
                    iterator.next((error: any, key: string, value: any) => {
                        if (error) {
                            return reject(error);
                        }
                        if (key === undefined) {
                            iterator.end(() => resolve(undefined));
                        } else {
                            processNext();
                        }
                    });
                };
                processNext();
            });

            this.logger.info('Database compaction completed');

        } catch (error) {
            this.logger.error('Failed to compact database', error);
            throw error;
        }
    }

    /**
     * 检查数据库是否存在
     */
    private async databaseExists(): Promise<boolean> {
        try {
            const stats = await fs.promises.stat(this.config.location);
            return stats.isDirectory();
        } catch (error) {
            return false;
        }
    }

    /**
     * 确保目录存在
     */
    private async ensureDirectory(dirPath: string): Promise<void> {
        try {
            await fs.promises.mkdir(path.dirname(dirPath), { recursive: true });
        } catch (error) {
            // 目录可能已存在
        }
    }

    /**
     * 获取数据库大小
     */
    private async getDatabaseSize(): Promise<number> {
        try {
            const stats = await fs.promises.stat(this.config.location);
            if (stats.isDirectory()) {
                return await this.getDirectorySize(this.config.location);
            }
            return stats.size;
        } catch (error) {
            return 0;
        }
    }

    /**
     * 获取目录大小
     */
    private async getDirectorySize(dirPath: string): Promise<number> {
        let totalSize = 0;
        try {
            const files = await fs.promises.readdir(dirPath);
            for (const file of files) {
                const filePath = path.join(dirPath, file);
                const stats = await fs.promises.stat(filePath);
                if (stats.isDirectory()) {
                    totalSize += await this.getDirectorySize(filePath);
                } else {
                    totalSize += stats.size;
                }
            }
        } catch (error) {
            // 忽略错误
        }
        return totalSize;
    }

    /**
     * 获取键数量
     */
    private async getKeyCount(): Promise<number> {
        let count = 0;
        try {
            const iterator = this.db.iterator({ keys: true, values: false });
            await new Promise((resolve, reject) => {
                const processNext = () => {
                    iterator.next((error: any, key: string) => {
                        if (error) {
                            return reject(error);
                        }
                        if (key === undefined) {
                            iterator.end(() => resolve(undefined));
                        } else {
                            count++;
                            processNext();
                        }
                    });
                };
                processNext();
            });
        } catch (error) {
            this.logger.warn('Failed to count keys', error);
        }
        return count;
    }

    /**
     * 获取近似大小
     */
    private async getApproximateSize(): Promise<number> {
        try {
            // LevelDB的近似大小计算
            return await new Promise((resolve, reject) => {
                this.db.approximateSize('', '~', (error: any, size: number) => {
                    if (error) {
                        reject(error);
                    } else {
                        resolve(size);
                    }
                });
            });
        } catch (error) {
            return 0;
        }
    }

    /**
     * 获取数据库属性
     */
    private async getDatabaseProperties(): Promise<any> {
        try {
            // 获取LevelDB属性
            const properties = {};
            const propertyNames = [
                'leveldb.num-files-at-level0',
                'leveldb.num-files-at-level1',
                'leveldb.num-files-at-level2',
                'leveldb.stats',
                'leveldb.sstables'
            ];

            for (const propName of propertyNames) {
                try {
                    const value = await new Promise((resolve, reject) => {
                        this.db.getProperty(propName, (error: any, value: any) => {
                            if (error) {
                                resolve(null);
                            } else {
                                resolve(value);
                            }
                        });
                    });
                    if (value !== null) {
                        properties[propName] = value;
                    }
                } catch (error) {
                    // 忽略单个属性获取失败
                }
            }

            return properties;
        } catch (error) {
            return {};
        }
    }

    /**
     * 等待队列完成
     */
    private async waitForQueueCompletion(): Promise<void> {
        while (this.isProcessingQueue || this.operationQueue.length > 0) {
            await new Promise(resolve => setTimeout(resolve, 100));
        }
    }
}
