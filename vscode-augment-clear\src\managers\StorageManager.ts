/**
 * Machine Code Reset Plugin Storage Manager
 * 存储数据管理器
 */

import * as vscode from 'vscode';
import * as fs from 'fs';
import * as path from 'path';
import { StorageData, CleanupTarget, SecurityOptions } from '../types';
import { Logger } from '../utils/logger';
import { CryptoUtils } from '../utils/crypto';
import { SECURITY_CONFIG } from '../utils/constants';

export class StorageManager {
    private context: vscode.ExtensionContext;
    private logger: Logger;

    constructor(context: vscode.ExtensionContext) {
        this.context = context;
        this.logger = Logger.getInstance(context);
    }

    /**
     * 重置所有存储数据
     */
    async resetStorage(secureDelete: boolean = true): Promise<void> {
        this.logger.info('Starting storage reset', { secureDelete });

        try {
            // 获取所有存储数据
            const storageData = await this.getAllStorageData();
            
            // 清除全局状态
            await this.clearGlobalState();
            
            // 清除工作空间状态
            await this.clearWorkspaceState();
            
            // 清除密钥存储
            await this.clearSecrets();
            
            // 清除内存数据
            await this.clearMemento();
            
            // 安全删除存储文件
            if (secureDelete) {
                await this.secureDeleteStorageFiles();
            }

            this.logger.info('Storage reset completed successfully');

        } catch (error) {
            this.logger.error('Failed to reset storage', error);
            throw error;
        }
    }

    /**
     * 获取所有存储数据
     */
    async getAllStorageData(): Promise<StorageData> {
        try {
            const globalState: Record<string, any> = {};
            const workspaceState: Record<string, any> = {};
            const secrets: Record<string, string> = {};
            const memento: Record<string, any> = {};

            // 获取全局状态数据
            const globalKeys = this.context.globalState.keys();
            for (const key of globalKeys) {
                globalState[key] = this.context.globalState.get(key);
            }

            // 获取工作空间状态数据
            if (this.context.workspaceState) {
                const workspaceKeys = this.context.workspaceState.keys();
                for (const key of workspaceKeys) {
                    workspaceState[key] = this.context.workspaceState.get(key);
                }
            }

            // 注意：出于安全考虑，我们不能直接读取secrets的内容
            // 这里只记录存在的密钥名称
            const commonSecretKeys = [
                'authToken', 'refreshToken', 'apiKey', 'sessionToken',
                'userCredentials', 'oauthToken', 'privateKey', 'certificate'
            ];

            for (const key of commonSecretKeys) {
                try {
                    const value = await this.context.secrets.get(key);
                    if (value) {
                        secrets[key] = '[REDACTED]'; // 不记录实际值
                    }
                } catch (error) {
                    // 密钥不存在或无法访问
                }
            }

            return {
                globalState,
                workspaceState,
                secrets,
                memento
            };

        } catch (error) {
            this.logger.error('Failed to get storage data', error);
            throw error;
        }
    }

    /**
     * 清除全局状态
     */
    private async clearGlobalState(): Promise<void> {
        try {
            const keys = this.context.globalState.keys();
            this.logger.debug(`Clearing ${keys.length} global state keys`);

            for (const key of keys) {
                await this.context.globalState.update(key, undefined);
            }

            this.logger.info('Global state cleared successfully');

        } catch (error) {
            this.logger.error('Failed to clear global state', error);
            throw error;
        }
    }

    /**
     * 清除工作空间状态
     */
    private async clearWorkspaceState(): Promise<void> {
        try {
            if (!this.context.workspaceState) {
                this.logger.debug('No workspace state to clear');
                return;
            }

            const keys = this.context.workspaceState.keys();
            this.logger.debug(`Clearing ${keys.length} workspace state keys`);

            for (const key of keys) {
                await this.context.workspaceState.update(key, undefined);
            }

            this.logger.info('Workspace state cleared successfully');

        } catch (error) {
            this.logger.error('Failed to clear workspace state', error);
            throw error;
        }
    }

    /**
     * 清除密钥存储
     */
    private async clearSecrets(): Promise<void> {
        try {
            const secretKeys = [
                'authToken', 'refreshToken', 'apiKey', 'sessionToken',
                'userCredentials', 'oauthToken', 'privateKey', 'certificate',
                'encryptionKey', 'signingKey', 'accessToken', 'idToken'
            ];

            let clearedCount = 0;
            for (const key of secretKeys) {
                try {
                    await this.context.secrets.delete(key);
                    clearedCount++;
                } catch (error) {
                    // 密钥可能不存在，这是正常的
                    this.logger.debug(`Secret key ${key} not found or already deleted`);
                }
            }

            this.logger.info(`Cleared ${clearedCount} secret keys`);

        } catch (error) {
            this.logger.error('Failed to clear secrets', error);
            throw error;
        }
    }

    /**
     * 清除内存数据
     */
    private async clearMemento(): Promise<void> {
        try {
            // 强制垃圾回收（如果可用）
            if (global.gc) {
                global.gc();
            }

            this.logger.info('Memory cleared successfully');

        } catch (error) {
            this.logger.error('Failed to clear memory', error);
            throw error;
        }
    }

    /**
     * 安全删除存储文件
     */
    private async secureDeleteStorageFiles(): Promise<void> {
        try {
            const targets = await this.getStorageFileTargets();
            const securityOptions: SecurityOptions = {
                overwritePasses: SECURITY_CONFIG.DEFAULT_OVERWRITE_PASSES,
                useRandomData: true,
                verifyDeletion: true,
                encryptBackups: false
            };

            for (const target of targets) {
                await this.secureDeleteFile(target.path, securityOptions);
            }

            this.logger.info(`Securely deleted ${targets.length} storage files`);

        } catch (error) {
            this.logger.error('Failed to securely delete storage files', error);
            throw error;
        }
    }

    /**
     * 获取存储文件目标
     */
    private async getStorageFileTargets(): Promise<CleanupTarget[]> {
        const targets: CleanupTarget[] = [];

        try {
            // 扩展存储路径
            const storagePaths = [
                this.context.globalStorageUri?.fsPath,
                this.context.storageUri?.fsPath,
                this.context.logUri?.fsPath
            ].filter(Boolean) as string[];

            for (const storagePath of storagePaths) {
                if (fs.existsSync(storagePath)) {
                    const files = await this.getFilesRecursively(storagePath);
                    for (const file of files) {
                        const stats = await fs.promises.stat(file);
                        targets.push({
                            path: file,
                            type: 'storage',
                            size: stats.size,
                            priority: 1,
                            secure: true
                        });
                    }
                }
            }

            return targets;

        } catch (error) {
            this.logger.error('Failed to get storage file targets', error);
            return [];
        }
    }

    /**
     * 递归获取文件列表
     */
    private async getFilesRecursively(dirPath: string): Promise<string[]> {
        const files: string[] = [];

        try {
            const entries = await fs.promises.readdir(dirPath, { withFileTypes: true });

            for (const entry of entries) {
                const fullPath = path.join(dirPath, entry.name);

                if (entry.isDirectory()) {
                    const subFiles = await this.getFilesRecursively(fullPath);
                    files.push(...subFiles);
                } else {
                    files.push(fullPath);
                }
            }

        } catch (error) {
            this.logger.warn(`Failed to read directory: ${dirPath}`, error);
        }

        return files;
    }

    /**
     * 安全删除文件
     */
    private async secureDeleteFile(filePath: string, options: SecurityOptions): Promise<void> {
        try {
            if (!fs.existsSync(filePath)) {
                return;
            }

            const stats = await fs.promises.stat(filePath);
            if (!stats.isFile()) {
                return;
            }

            // 多次覆写文件内容
            for (let pass = 0; pass < options.overwritePasses; pass++) {
                const randomData = options.useRandomData 
                    ? CryptoUtils.generateRandomBytes(stats.size)
                    : Buffer.alloc(stats.size, 0);

                await fs.promises.writeFile(filePath, randomData);
                await fs.promises.fsync(await fs.promises.open(filePath, 'r+'));
            }

            // 删除文件
            await fs.promises.unlink(filePath);

            // 验证删除
            if (options.verifyDeletion && fs.existsSync(filePath)) {
                throw new Error(`File still exists after deletion: ${filePath}`);
            }

            this.logger.debug(`Securely deleted file: ${filePath}`);

        } catch (error) {
            this.logger.error(`Failed to securely delete file: ${filePath}`, error);
            throw error;
        }
    }

    /**
     * 获取存储统计信息
     */
    async getStorageStatistics(): Promise<any> {
        try {
            const storageData = await this.getAllStorageData();
            const targets = await this.getStorageFileTargets();

            const totalFileSize = targets.reduce((sum, target) => sum + target.size, 0);
            const fileCount = targets.length;

            return {
                globalStateKeys: Object.keys(storageData.globalState).length,
                workspaceStateKeys: Object.keys(storageData.workspaceState).length,
                secretKeys: Object.keys(storageData.secrets).length,
                mementoKeys: Object.keys(storageData.memento).length,
                storageFiles: {
                    count: fileCount,
                    totalSize: totalFileSize,
                    averageSize: fileCount > 0 ? totalFileSize / fileCount : 0
                },
                storagePaths: {
                    global: this.context.globalStorageUri?.fsPath,
                    workspace: this.context.storageUri?.fsPath,
                    logs: this.context.logUri?.fsPath
                }
            };

        } catch (error) {
            this.logger.error('Failed to get storage statistics', error);
            throw error;
        }
    }

    /**
     * 备份存储数据
     */
    async backupStorageData(): Promise<string> {
        try {
            const storageData = await this.getAllStorageData();
            const backupData = {
                timestamp: new Date().toISOString(),
                data: storageData
            };

            const backupContent = JSON.stringify(backupData, null, 2);
            const backupFileName = `storage-backup-${Date.now()}.json`;
            const backupPath = path.join(
                this.context.globalStorageUri?.fsPath || '',
                'backups',
                backupFileName
            );

            // 确保备份目录存在
            await fs.promises.mkdir(path.dirname(backupPath), { recursive: true });

            // 写入备份文件
            await fs.promises.writeFile(backupPath, backupContent, 'utf8');

            this.logger.info(`Storage data backed up to: ${backupPath}`);
            return backupPath;

        } catch (error) {
            this.logger.error('Failed to backup storage data', error);
            throw error;
        }
    }
}
