/**
 * Production-Grade Network and API Manager
 * 基于outps.js分析的完整网络连接和API状态重置
 */

import * as https from 'https';
import * as http from 'http';
import * as WebSocket from 'ws';
import axios, { AxiosInstance, AxiosRequestConfig } from 'axios';
import * as jwt from 'jsonwebtoken';
import { Logger } from '../utils/logger';
import { CryptoUtils } from '../utils/crypto';

export interface NetworkConfig {
    apiBaseUrl: string;
    timeout: number;
    retryAttempts: number;
    retryDelay: number;
    maxConcurrentRequests: number;
    userAgent: string;
    enableCompression: boolean;
    enableKeepAlive: boolean;
    maxSockets: number;
}

export interface AuthTokens {
    accessToken?: string;
    refreshToken?: string;
    idToken?: string;
    apiKey?: string;
    sessionToken?: string;
    oauthToken?: string;
    bearerToken?: string;
    customTokens: Map<string, string>;
}

export interface ConnectionState {
    isConnected: boolean;
    lastConnected?: Date;
    lastDisconnected?: Date;
    connectionCount: number;
    errorCount: number;
    lastError?: string;
    activeConnections: number;
    pendingRequests: number;
}

export interface ApiEndpoint {
    url: string;
    method: string;
    headers: Record<string, string>;
    timeout: number;
    retries: number;
}

export class NetworkManager {
    private logger: Logger;
    private config: NetworkConfig;
    private httpClient: AxiosInstance;
    private httpsAgent: https.Agent;
    private httpAgent: http.Agent;
    private authTokens: AuthTokens;
    private connectionState: ConnectionState;
    private activeWebSockets: Set<WebSocket> = new Set();
    private requestQueue: Array<() => Promise<any>> = [];
    private isProcessingQueue: boolean = false;

    constructor(config: NetworkConfig, logger: Logger) {
        this.config = config;
        this.logger = logger;
        this.authTokens = { customTokens: new Map() };
        this.connectionState = {
            isConnected: false,
            connectionCount: 0,
            errorCount: 0,
            activeConnections: 0,
            pendingRequests: 0
        };

        this.initializeAgents();
        this.initializeHttpClient();
    }

    /**
     * 初始化HTTP代理
     */
    private initializeAgents(): void {
        // HTTPS代理配置
        this.httpsAgent = new https.Agent({
            keepAlive: this.config.enableKeepAlive,
            maxSockets: this.config.maxSockets,
            maxFreeSockets: 10,
            timeout: this.config.timeout,
            freeSocketTimeout: 30000,
            rejectUnauthorized: true
        });

        // HTTP代理配置
        this.httpAgent = new http.Agent({
            keepAlive: this.config.enableKeepAlive,
            maxSockets: this.config.maxSockets,
            maxFreeSockets: 10,
            timeout: this.config.timeout,
            freeSocketTimeout: 30000
        });
    }

    /**
     * 初始化HTTP客户端
     */
    private initializeHttpClient(): void {
        this.httpClient = axios.create({
            baseURL: this.config.apiBaseUrl,
            timeout: this.config.timeout,
            maxRedirects: 5,
            maxContentLength: 50 * 1024 * 1024, // 50MB
            maxBodyLength: 50 * 1024 * 1024,
            httpsAgent: this.httpsAgent,
            httpAgent: this.httpAgent,
            headers: {
                'User-Agent': this.config.userAgent,
                'Accept': 'application/json',
                'Content-Type': 'application/json'
            },
            decompress: this.config.enableCompression
        });

        // 请求拦截器
        this.httpClient.interceptors.request.use(
            (config) => {
                this.connectionState.pendingRequests++;
                this.addAuthHeaders(config);
                this.logger.debug('HTTP request started', { 
                    url: config.url, 
                    method: config.method 
                });
                return config;
            },
            (error) => {
                this.connectionState.pendingRequests--;
                this.connectionState.errorCount++;
                this.logger.error('HTTP request error', error);
                return Promise.reject(error);
            }
        );

        // 响应拦截器
        this.httpClient.interceptors.response.use(
            (response) => {
                this.connectionState.pendingRequests--;
                this.connectionState.isConnected = true;
                this.connectionState.lastConnected = new Date();
                this.logger.debug('HTTP response received', { 
                    status: response.status,
                    url: response.config.url 
                });
                return response;
            },
            (error) => {
                this.connectionState.pendingRequests--;
                this.connectionState.errorCount++;
                this.connectionState.lastError = error.message;
                this.logger.error('HTTP response error', error);
                return Promise.reject(error);
            }
        );
    }

    /**
     * 添加认证头
     */
    private addAuthHeaders(config: AxiosRequestConfig): void {
        if (!config.headers) {
            config.headers = {};
        }

        // Bearer Token
        if (this.authTokens.bearerToken) {
            config.headers['Authorization'] = `Bearer ${this.authTokens.bearerToken}`;
        }

        // API Key
        if (this.authTokens.apiKey) {
            config.headers['X-API-Key'] = this.authTokens.apiKey;
        }

        // Session Token
        if (this.authTokens.sessionToken) {
            config.headers['X-Session-Token'] = this.authTokens.sessionToken;
        }

        // OAuth Token
        if (this.authTokens.oauthToken) {
            config.headers['X-OAuth-Token'] = this.authTokens.oauthToken;
        }

        // 自定义令牌
        this.authTokens.customTokens.forEach((value, key) => {
            config.headers![key] = value;
        });
    }

    /**
     * 重置所有网络连接和状态
     */
    async resetNetworkState(): Promise<void> {
        try {
            this.logger.info('Starting network state reset');

            // 清除所有认证令牌
            await this.clearAllTokens();

            // 关闭所有WebSocket连接
            await this.closeAllWebSockets();

            // 清除HTTP连接池
            await this.clearConnectionPools();

            // 重置连接状态
            this.resetConnectionState();

            // 重新初始化网络组件
            this.initializeAgents();
            this.initializeHttpClient();

            this.logger.info('Network state reset completed');

        } catch (error) {
            this.logger.error('Failed to reset network state', error);
            throw new Error(`Network reset failed: ${error}`);
        }
    }

    /**
     * 清除所有认证令牌
     */
    async clearAllTokens(): Promise<void> {
        try {
            this.logger.info('Clearing all authentication tokens');

            // 如果有刷新令牌，尝试撤销
            if (this.authTokens.refreshToken) {
                try {
                    await this.revokeToken(this.authTokens.refreshToken);
                } catch (error) {
                    this.logger.warn('Failed to revoke refresh token', error);
                }
            }

            // 如果有访问令牌，尝试撤销
            if (this.authTokens.accessToken) {
                try {
                    await this.revokeToken(this.authTokens.accessToken);
                } catch (error) {
                    this.logger.warn('Failed to revoke access token', error);
                }
            }

            // 清除所有令牌
            this.authTokens = { customTokens: new Map() };

            this.logger.info('All authentication tokens cleared');

        } catch (error) {
            this.logger.error('Failed to clear tokens', error);
            throw error;
        }
    }

    /**
     * 撤销令牌
     */
    private async revokeToken(token: string): Promise<void> {
        try {
            // 尝试解析JWT令牌获取撤销端点
            const decoded = jwt.decode(token, { complete: true });
            if (decoded && typeof decoded === 'object' && decoded.payload) {
                const payload = decoded.payload as any;
                if (payload.iss) {
                    // 构造撤销URL
                    const revokeUrl = `${payload.iss}/oauth/revoke`;
                    await this.httpClient.post(revokeUrl, { token });
                }
            }
        } catch (error) {
            // 撤销失败不是致命错误
            this.logger.debug('Token revocation failed', error);
        }
    }

    /**
     * 关闭所有WebSocket连接
     */
    async closeAllWebSockets(): Promise<void> {
        try {
            this.logger.info(`Closing ${this.activeWebSockets.size} WebSocket connections`);

            const closePromises = Array.from(this.activeWebSockets).map(ws => {
                return new Promise<void>((resolve) => {
                    if (ws.readyState === WebSocket.OPEN) {
                        ws.close(1000, 'Network reset');
                        ws.on('close', () => resolve());
                        // 超时保护
                        setTimeout(() => resolve(), 5000);
                    } else {
                        resolve();
                    }
                });
            });

            await Promise.all(closePromises);
            this.activeWebSockets.clear();

            this.logger.info('All WebSocket connections closed');

        } catch (error) {
            this.logger.error('Failed to close WebSocket connections', error);
            throw error;
        }
    }

    /**
     * 清除HTTP连接池
     */
    async clearConnectionPools(): Promise<void> {
        try {
            this.logger.info('Clearing HTTP connection pools');

            // 销毁HTTPS代理
            if (this.httpsAgent) {
                this.httpsAgent.destroy();
            }

            // 销毁HTTP代理
            if (this.httpAgent) {
                this.httpAgent.destroy();
            }

            // 等待连接关闭
            await new Promise(resolve => setTimeout(resolve, 1000));

            this.logger.info('HTTP connection pools cleared');

        } catch (error) {
            this.logger.error('Failed to clear connection pools', error);
            throw error;
        }
    }

    /**
     * 重置连接状态
     */
    private resetConnectionState(): void {
        this.connectionState = {
            isConnected: false,
            lastDisconnected: new Date(),
            connectionCount: 0,
            errorCount: 0,
            activeConnections: 0,
            pendingRequests: 0
        };
    }

    /**
     * 设置认证令牌
     */
    setAuthToken(type: keyof AuthTokens, token: string): void {
        if (type === 'customTokens') {
            throw new Error('Use setCustomToken for custom tokens');
        }
        (this.authTokens as any)[type] = token;
        this.logger.debug(`Auth token set: ${type}`);
    }

    /**
     * 设置自定义令牌
     */
    setCustomToken(key: string, value: string): void {
        this.authTokens.customTokens.set(key, value);
        this.logger.debug(`Custom token set: ${key}`);
    }

    /**
     * 获取连接状态
     */
    getConnectionState(): ConnectionState {
        return { ...this.connectionState };
    }

    /**
     * 测试网络连接
     */
    async testConnection(): Promise<boolean> {
        try {
            const response = await this.httpClient.get('/health', { timeout: 5000 });
            return response.status === 200;
        } catch (error) {
            this.logger.warn('Connection test failed', error);
            return false;
        }
    }

    /**
     * 创建WebSocket连接
     */
    createWebSocket(url: string, protocols?: string[]): WebSocket {
        const ws = new WebSocket(url, protocols);
        
        ws.on('open', () => {
            this.activeWebSockets.add(ws);
            this.connectionState.activeConnections++;
            this.connectionState.connectionCount++;
            this.logger.debug('WebSocket connection opened', { url });
        });

        ws.on('close', () => {
            this.activeWebSockets.delete(ws);
            this.connectionState.activeConnections--;
            this.logger.debug('WebSocket connection closed', { url });
        });

        ws.on('error', (error) => {
            this.connectionState.errorCount++;
            this.connectionState.lastError = error.message;
            this.logger.error('WebSocket error', error);
        });

        return ws;
    }

    /**
     * 获取网络统计信息
     */
    getNetworkStats(): any {
        return {
            connectionState: this.connectionState,
            activeWebSockets: this.activeWebSockets.size,
            authTokensCount: Object.keys(this.authTokens).length + this.authTokens.customTokens.size,
            httpClientDefaults: this.httpClient.defaults,
            agentStats: {
                httpsAgent: {
                    maxSockets: this.httpsAgent.maxSockets,
                    sockets: Object.keys(this.httpsAgent.sockets).length,
                    freeSockets: Object.keys(this.httpsAgent.freeSockets).length
                },
                httpAgent: {
                    maxSockets: this.httpAgent.maxSockets,
                    sockets: Object.keys(this.httpAgent.sockets).length,
                    freeSockets: Object.keys(this.httpAgent.freeSockets).length
                }
            }
        };
    }

    /**
     * 销毁网络管理器
     */
    async destroy(): Promise<void> {
        try {
            await this.resetNetworkState();
            this.logger.info('Network manager destroyed');
        } catch (error) {
            this.logger.error('Failed to destroy network manager', error);
            throw error;
        }
    }
}
