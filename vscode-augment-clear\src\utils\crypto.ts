/**
 * Machine Code Reset Plugin Crypto Utilities
 * 加密和安全相关工具函数
 */

import * as crypto from 'crypto';
import { v4 as uuidv4, v1 as uuidv1, v5 as uuidv5 } from 'uuid';
import { HardwareInfo } from '../types';

export class CryptoUtils {
    private static readonly ALGORITHM = 'aes-256-gcm';
    private static readonly KEY_LENGTH = 32;
    private static readonly IV_LENGTH = 16;
    private static readonly TAG_LENGTH = 16;

    /**
     * 生成UUID v4
     */
    static generateUUID(): string {
        return uuidv4();
    }

    /**
     * 生成UUID v1 (基于时间戳)
     */
    static generateTimeBasedUUID(): string {
        return uuidv1();
    }

    /**
     * 生成UUID v5 (基于命名空间)
     */
    static generateNameBasedUUID(name: string, namespace: string): string {
        return uuidv5(name, namespace);
    }

    /**
     * 生成随机字符串
     */
    static generateRandomString(length: number = 32): string {
        return crypto.randomBytes(length).toString('hex');
    }

    /**
     * 生成安全的随机数
     */
    static generateSecureRandom(min: number = 0, max: number = 1000000): number {
        const range = max - min;
        const bytesNeeded = Math.ceil(Math.log2(range) / 8);
        const randomBytes = crypto.randomBytes(bytesNeeded);
        const randomValue = randomBytes.readUIntBE(0, bytesNeeded);
        return min + (randomValue % range);
    }

    /**
     * 创建SHA256哈希
     */
    static createHash(data: string): string {
        return crypto.createHash('sha256').update(data).digest('hex');
    }

    /**
     * 创建HMAC
     */
    static createHMAC(data: string, key: string): string {
        return crypto.createHmac('sha256', key).update(data).digest('hex');
    }

    /**
     * 加密数据
     */
    static encrypt(data: string, password: string): string {
        try {
            const key = crypto.scryptSync(password, 'salt', this.KEY_LENGTH);
            const iv = crypto.randomBytes(this.IV_LENGTH);
            const cipher = crypto.createCipher(this.ALGORITHM, key);
            
            let encrypted = cipher.update(data, 'utf8', 'hex');
            encrypted += cipher.final('hex');
            
            const tag = cipher.getAuthTag();
            
            return iv.toString('hex') + ':' + tag.toString('hex') + ':' + encrypted;
        } catch (error) {
            throw new Error(`Encryption failed: ${error}`);
        }
    }

    /**
     * 解密数据
     */
    static decrypt(encryptedData: string, password: string): string {
        try {
            const parts = encryptedData.split(':');
            if (parts.length !== 3) {
                throw new Error('Invalid encrypted data format');
            }

            const key = crypto.scryptSync(password, 'salt', this.KEY_LENGTH);
            const iv = Buffer.from(parts[0], 'hex');
            const tag = Buffer.from(parts[1], 'hex');
            const encrypted = parts[2];

            const decipher = crypto.createDecipher(this.ALGORITHM, key);
            decipher.setAuthTag(tag);

            let decrypted = decipher.update(encrypted, 'hex', 'utf8');
            decrypted += decipher.final('utf8');

            return decrypted;
        } catch (error) {
            throw new Error(`Decryption failed: ${error}`);
        }
    }

    /**
     * 生成设备指纹
     */
    static generateDeviceFingerprint(hardwareInfo: HardwareInfo): string {
        const fingerprint = [
            hardwareInfo.cpuId,
            hardwareInfo.motherboardId,
            hardwareInfo.diskId,
            hardwareInfo.networkMac,
            hardwareInfo.biosId,
            hardwareInfo.systemUuid
        ].filter(Boolean).join('|');

        return this.createHash(fingerprint);
    }

    /**
     * 生成机器ID
     */
    static generateMachineId(hardwareInfo: HardwareInfo): string {
        const machineData = [
            hardwareInfo.cpuId,
            hardwareInfo.motherboardId,
            hardwareInfo.systemUuid
        ].filter(Boolean).join('|');

        const hash = this.createHash(machineData);
        return hash.substring(0, 16).toUpperCase();
    }

    /**
     * 生成会话ID
     */
    static generateSessionId(): string {
        const timestamp = Date.now().toString();
        const random = this.generateRandomString(16);
        const combined = timestamp + random;
        return this.createHash(combined).substring(0, 24);
    }

    /**
     * 验证UUID格式
     */
    static isValidUUID(uuid: string): boolean {
        const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
        return uuidRegex.test(uuid);
    }

    /**
     * 安全比较两个字符串
     */
    static secureCompare(a: string, b: string): boolean {
        if (a.length !== b.length) {
            return false;
        }

        let result = 0;
        for (let i = 0; i < a.length; i++) {
            result |= a.charCodeAt(i) ^ b.charCodeAt(i);
        }

        return result === 0;
    }

    /**
     * 生成随机字节数组
     */
    static generateRandomBytes(size: number): Buffer {
        return crypto.randomBytes(size);
    }

    /**
     * 安全删除字符串（内存清理）
     */
    static secureStringClear(str: string): void {
        // 在JavaScript中，字符串是不可变的，这里只是概念性的清理
        // 实际的内存清理由垃圾回收器处理
        if (global.gc) {
            global.gc();
        }
    }

    /**
     * 生成密钥派生函数
     */
    static deriveKey(password: string, salt: string, iterations: number = 100000): Buffer {
        return crypto.pbkdf2Sync(password, salt, iterations, this.KEY_LENGTH, 'sha256');
    }

    /**
     * 创建数字签名
     */
    static createSignature(data: string, privateKey: string): string {
        const sign = crypto.createSign('RSA-SHA256');
        sign.update(data);
        return sign.sign(privateKey, 'hex');
    }

    /**
     * 验证数字签名
     */
    static verifySignature(data: string, signature: string, publicKey: string): boolean {
        try {
            const verify = crypto.createVerify('RSA-SHA256');
            verify.update(data);
            return verify.verify(publicKey, signature, 'hex');
        } catch (error) {
            return false;
        }
    }

    /**
     * 生成RSA密钥对
     */
    static generateKeyPair(): { publicKey: string; privateKey: string } {
        const { publicKey, privateKey } = crypto.generateKeyPairSync('rsa', {
            modulusLength: 2048,
            publicKeyEncoding: {
                type: 'spki',
                format: 'pem'
            },
            privateKeyEncoding: {
                type: 'pkcs8',
                format: 'pem'
            }
        });

        return { publicKey, privateKey };
    }

    /**
     * 计算文件哈希
     */
    static calculateFileHash(filePath: string): Promise<string> {
        return new Promise((resolve, reject) => {
            const hash = crypto.createHash('sha256');
            const fs = require('fs');
            const stream = fs.createReadStream(filePath);

            stream.on('data', (data: Buffer) => {
                hash.update(data);
            });

            stream.on('end', () => {
                resolve(hash.digest('hex'));
            });

            stream.on('error', (error: Error) => {
                reject(error);
            });
        });
    }
}
