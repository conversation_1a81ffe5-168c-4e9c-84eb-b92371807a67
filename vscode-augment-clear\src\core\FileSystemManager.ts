/**
 * Production-Grade File System Manager
 * 基于outps.js分析的完整文件系统操作和清理
 */

import * as fs from 'fs';
import * as path from 'path';
import * as crypto from 'crypto';
import * as glob from 'fast-glob';
import * as chokidar from 'chokidar';
import { promisify } from 'util';
import { Logger } from '../utils/logger';
import { CryptoUtils } from '../utils/crypto';

const stat = promisify(fs.stat);
const readdir = promisify(fs.readdir);
const unlink = promisify(fs.unlink);
const rmdir = promisify(fs.rmdir);
const mkdir = promisify(fs.mkdir);
const writeFile = promisify(fs.writeFile);
const readFile = promisify(fs.readFile);

export interface FileSystemConfig {
    basePath: string;
    tempPath: string;
    backupPath: string;
    maxFileSize: number;
    allowedExtensions: string[];
    blockedPaths: string[];
    secureDeletePasses: number;
    enableWatching: boolean;
    watchIgnorePatterns: string[];
}

export interface FileInfo {
    path: string;
    name: string;
    size: number;
    type: 'file' | 'directory' | 'symlink';
    created: Date;
    modified: Date;
    accessed: Date;
    permissions: string;
    hash?: string;
    isHidden: boolean;
    extension?: string;
}

export interface DirectoryStats {
    path: string;
    totalFiles: number;
    totalDirectories: number;
    totalSize: number;
    largestFile: FileInfo | null;
    oldestFile: FileInfo | null;
    newestFile: FileInfo | null;
}

export interface CleanupResult {
    deletedFiles: number;
    deletedDirectories: number;
    totalSize: number;
    errors: string[];
    duration: number;
}

export interface FileWatcher {
    path: string;
    watcher: chokidar.FSWatcher;
    events: Array<{ type: string; path: string; timestamp: Date }>;
}

export class FileSystemManager {
    private logger: Logger;
    private config: FileSystemConfig;
    private watchers: Map<string, FileWatcher> = new Map();
    private fileCache: Map<string, FileInfo> = new Map();
    private cacheTimeout: number = 5 * 60 * 1000; // 5分钟缓存

    constructor(config: FileSystemConfig, logger: Logger) {
        this.config = config;
        this.logger = logger;
    }

    /**
     * 初始化文件系统管理器
     */
    async initialize(): Promise<void> {
        try {
            this.logger.info('Initializing file system manager');

            // 确保基础目录存在
            await this.ensureDirectories();

            // 启动文件监控
            if (this.config.enableWatching) {
                await this.startWatching();
            }

            this.logger.info('File system manager initialized');

        } catch (error) {
            this.logger.error('Failed to initialize file system manager', error);
            throw error;
        }
    }

    /**
     * 完全清理文件系统 - 基于outps.js的文件清理功能
     */
    async performCompleteCleanup(): Promise<CleanupResult> {
        const startTime = Date.now();
        const result: CleanupResult = {
            deletedFiles: 0,
            deletedDirectories: 0,
            totalSize: 0,
            errors: [],
            duration: 0
        };

        try {
            this.logger.info('Starting complete file system cleanup');

            // 停止文件监控
            await this.stopAllWatching();

            // 清理临时文件
            const tempResult = await this.cleanupTemporaryFiles();
            result.deletedFiles += tempResult.deletedFiles;
            result.totalSize += tempResult.totalSize;
            result.errors.push(...tempResult.errors);

            // 清理缓存文件
            const cacheResult = await this.cleanupCacheFiles();
            result.deletedFiles += cacheResult.deletedFiles;
            result.totalSize += cacheResult.totalSize;
            result.errors.push(...cacheResult.errors);

            // 清理日志文件
            const logResult = await this.cleanupLogFiles();
            result.deletedFiles += logResult.deletedFiles;
            result.totalSize += logResult.totalSize;
            result.errors.push(...logResult.errors);

            // 清理用户数据文件
            const userDataResult = await this.cleanupUserDataFiles();
            result.deletedFiles += userDataResult.deletedFiles;
            result.totalSize += userDataResult.totalSize;
            result.errors.push(...userDataResult.errors);

            // 清理空目录
            const dirResult = await this.cleanupEmptyDirectories();
            result.deletedDirectories += dirResult.deletedDirectories;
            result.errors.push(...dirResult.errors);

            // 清理文件缓存
            this.clearFileCache();

            result.duration = Date.now() - startTime;
            this.logger.info('Complete file system cleanup finished', result);

            return result;

        } catch (error) {
            result.duration = Date.now() - startTime;
            result.errors.push(`Cleanup failed: ${error}`);
            this.logger.error('File system cleanup failed', error);
            return result;
        }
    }

    /**
     * 安全删除文件 - 多重覆写
     */
    async secureDeleteFile(filePath: string): Promise<void> {
        try {
            if (!await this.fileExists(filePath)) {
                return;
            }

            const stats = await stat(filePath);
            if (!stats.isFile()) {
                throw new Error('Path is not a file');
            }

            this.logger.debug('Starting secure file deletion', { filePath, size: stats.size });

            // 多重覆写
            for (let pass = 0; pass < this.config.secureDeletePasses; pass++) {
                // 生成随机数据
                const randomData = crypto.randomBytes(stats.size);
                
                // 覆写文件
                await writeFile(filePath, randomData);
                
                // 强制同步到磁盘
                const fd = await fs.promises.open(filePath, 'r+');
                await fd.sync();
                await fd.close();

                this.logger.debug(`Secure delete pass ${pass + 1}/${this.config.secureDeletePasses} completed`);
            }

            // 最终删除文件
            await unlink(filePath);

            // 验证删除
            if (await this.fileExists(filePath)) {
                throw new Error('File still exists after secure deletion');
            }

            this.logger.debug('Secure file deletion completed', { filePath });

        } catch (error) {
            this.logger.error('Secure file deletion failed', { filePath, error });
            throw error;
        }
    }

    /**
     * 安全删除目录
     */
    async secureDeleteDirectory(dirPath: string): Promise<void> {
        try {
            if (!await this.directoryExists(dirPath)) {
                return;
            }

            this.logger.debug('Starting secure directory deletion', { dirPath });

            // 递归删除所有文件
            const files = await this.getAllFiles(dirPath);
            for (const file of files) {
                await this.secureDeleteFile(file.path);
            }

            // 删除空目录
            await this.removeEmptyDirectories(dirPath);

            this.logger.debug('Secure directory deletion completed', { dirPath });

        } catch (error) {
            this.logger.error('Secure directory deletion failed', { dirPath, error });
            throw error;
        }
    }

    /**
     * 获取文件信息
     */
    async getFileInfo(filePath: string, useCache: boolean = true): Promise<FileInfo | null> {
        try {
            // 检查缓存
            if (useCache && this.fileCache.has(filePath)) {
                const cached = this.fileCache.get(filePath)!;
                if (Date.now() - cached.modified.getTime() < this.cacheTimeout) {
                    return cached;
                }
            }

            if (!await this.fileExists(filePath)) {
                return null;
            }

            const stats = await stat(filePath);
            const parsedPath = path.parse(filePath);

            const fileInfo: FileInfo = {
                path: filePath,
                name: parsedPath.base,
                size: stats.size,
                type: stats.isFile() ? 'file' : stats.isDirectory() ? 'directory' : 'symlink',
                created: stats.birthtime,
                modified: stats.mtime,
                accessed: stats.atime,
                permissions: stats.mode.toString(8),
                isHidden: parsedPath.name.startsWith('.'),
                extension: parsedPath.ext
            };

            // 计算文件哈希（仅对小文件）
            if (stats.isFile() && stats.size < 1024 * 1024) { // 1MB以下
                try {
                    const content = await readFile(filePath);
                    fileInfo.hash = crypto.createHash('sha256').update(content).digest('hex');
                } catch (error) {
                    // 忽略哈希计算错误
                }
            }

            // 缓存结果
            if (useCache) {
                this.fileCache.set(filePath, fileInfo);
            }

            return fileInfo;

        } catch (error) {
            this.logger.error('Failed to get file info', { filePath, error });
            return null;
        }
    }

    /**
     * 获取目录统计信息
     */
    async getDirectoryStats(dirPath: string): Promise<DirectoryStats> {
        try {
            const files = await this.getAllFiles(dirPath);
            const directories = await this.getAllDirectories(dirPath);

            let totalSize = 0;
            let largestFile: FileInfo | null = null;
            let oldestFile: FileInfo | null = null;
            let newestFile: FileInfo | null = null;

            for (const file of files) {
                totalSize += file.size;

                if (!largestFile || file.size > largestFile.size) {
                    largestFile = file;
                }

                if (!oldestFile || file.created < oldestFile.created) {
                    oldestFile = file;
                }

                if (!newestFile || file.created > newestFile.created) {
                    newestFile = file;
                }
            }

            return {
                path: dirPath,
                totalFiles: files.length,
                totalDirectories: directories.length,
                totalSize,
                largestFile,
                oldestFile,
                newestFile
            };

        } catch (error) {
            this.logger.error('Failed to get directory stats', { dirPath, error });
            throw error;
        }
    }

    /**
     * 清理临时文件
     */
    private async cleanupTemporaryFiles(): Promise<CleanupResult> {
        const result: CleanupResult = {
            deletedFiles: 0,
            deletedDirectories: 0,
            totalSize: 0,
            errors: [],
            duration: 0
        };

        try {
            const tempPatterns = [
                path.join(this.config.tempPath, '**/*'),
                path.join(this.config.basePath, '**/*.tmp'),
                path.join(this.config.basePath, '**/*.temp'),
                path.join(this.config.basePath, '**/~*'),
                path.join(this.config.basePath, '**/.DS_Store'),
                path.join(this.config.basePath, '**/Thumbs.db')
            ];

            for (const pattern of tempPatterns) {
                const files = await glob(pattern, { onlyFiles: true });
                for (const file of files) {
                    try {
                        const stats = await stat(file);
                        await this.secureDeleteFile(file);
                        result.deletedFiles++;
                        result.totalSize += stats.size;
                    } catch (error) {
                        result.errors.push(`Failed to delete ${file}: ${error}`);
                    }
                }
            }

        } catch (error) {
            result.errors.push(`Temp cleanup failed: ${error}`);
        }

        return result;
    }

    /**
     * 清理缓存文件
     */
    private async cleanupCacheFiles(): Promise<CleanupResult> {
        const result: CleanupResult = {
            deletedFiles: 0,
            deletedDirectories: 0,
            totalSize: 0,
            errors: [],
            duration: 0
        };

        try {
            const cachePatterns = [
                path.join(this.config.basePath, '**/cache/**/*'),
                path.join(this.config.basePath, '**/.cache/**/*'),
                path.join(this.config.basePath, '**/node_modules/.cache/**/*'),
                path.join(this.config.basePath, '**/*.cache')
            ];

            for (const pattern of cachePatterns) {
                const files = await glob(pattern, { onlyFiles: true });
                for (const file of files) {
                    try {
                        const stats = await stat(file);
                        await this.secureDeleteFile(file);
                        result.deletedFiles++;
                        result.totalSize += stats.size;
                    } catch (error) {
                        result.errors.push(`Failed to delete cache file ${file}: ${error}`);
                    }
                }
            }

        } catch (error) {
            result.errors.push(`Cache cleanup failed: ${error}`);
        }

        return result;
    }

    /**
     * 清理日志文件
     */
    private async cleanupLogFiles(): Promise<CleanupResult> {
        const result: CleanupResult = {
            deletedFiles: 0,
            deletedDirectories: 0,
            totalSize: 0,
            errors: [],
            duration: 0
        };

        try {
            const logPatterns = [
                path.join(this.config.basePath, '**/*.log'),
                path.join(this.config.basePath, '**/logs/**/*'),
                path.join(this.config.basePath, '**/.logs/**/*')
            ];

            for (const pattern of logPatterns) {
                const files = await glob(pattern, { onlyFiles: true });
                for (const file of files) {
                    try {
                        const stats = await stat(file);
                        await this.secureDeleteFile(file);
                        result.deletedFiles++;
                        result.totalSize += stats.size;
                    } catch (error) {
                        result.errors.push(`Failed to delete log file ${file}: ${error}`);
                    }
                }
            }

        } catch (error) {
            result.errors.push(`Log cleanup failed: ${error}`);
        }

        return result;
    }

    /**
     * 清理用户数据文件
     */
    private async cleanupUserDataFiles(): Promise<CleanupResult> {
        const result: CleanupResult = {
            deletedFiles: 0,
            deletedDirectories: 0,
            totalSize: 0,
            errors: [],
            duration: 0
        };

        try {
            const userDataPatterns = [
                path.join(this.config.basePath, '**/user-data/**/*'),
                path.join(this.config.basePath, '**/userData/**/*'),
                path.join(this.config.basePath, '**/.vscode/**/*'),
                path.join(this.config.basePath, '**/workspace-state/**/*')
            ];

            for (const pattern of userDataPatterns) {
                const files = await glob(pattern, { onlyFiles: true });
                for (const file of files) {
                    try {
                        const stats = await stat(file);
                        await this.secureDeleteFile(file);
                        result.deletedFiles++;
                        result.totalSize += stats.size;
                    } catch (error) {
                        result.errors.push(`Failed to delete user data file ${file}: ${error}`);
                    }
                }
            }

        } catch (error) {
            result.errors.push(`User data cleanup failed: ${error}`);
        }

        return result;
    }

    // 辅助方法

    private async fileExists(filePath: string): Promise<boolean> {
        try {
            await stat(filePath);
            return true;
        } catch {
            return false;
        }
    }

    private async directoryExists(dirPath: string): Promise<boolean> {
        try {
            const stats = await stat(dirPath);
            return stats.isDirectory();
        } catch {
            return false;
        }
    }

    private async ensureDirectories(): Promise<void> {
        const dirs = [this.config.basePath, this.config.tempPath, this.config.backupPath];
        for (const dir of dirs) {
            try {
                await mkdir(dir, { recursive: true });
            } catch (error) {
                // 目录可能已存在
            }
        }
    }

    private async getAllFiles(dirPath: string): Promise<FileInfo[]> {
        const files: FileInfo[] = [];
        const pattern = path.join(dirPath, '**/*');
        const filePaths = await glob(pattern, { onlyFiles: true });
        
        for (const filePath of filePaths) {
            const fileInfo = await this.getFileInfo(filePath, false);
            if (fileInfo) {
                files.push(fileInfo);
            }
        }
        
        return files;
    }

    private async getAllDirectories(dirPath: string): Promise<string[]> {
        const pattern = path.join(dirPath, '**/*');
        return glob(pattern, { onlyDirectories: true });
    }

    private async cleanupEmptyDirectories(): Promise<CleanupResult> {
        const result: CleanupResult = {
            deletedFiles: 0,
            deletedDirectories: 0,
            totalSize: 0,
            errors: [],
            duration: 0
        };

        try {
            const directories = await this.getAllDirectories(this.config.basePath);
            
            // 从最深的目录开始删除
            directories.sort((a, b) => b.split(path.sep).length - a.split(path.sep).length);
            
            for (const dir of directories) {
                try {
                    const files = await readdir(dir);
                    if (files.length === 0) {
                        await rmdir(dir);
                        result.deletedDirectories++;
                    }
                } catch (error) {
                    result.errors.push(`Failed to delete directory ${dir}: ${error}`);
                }
            }

        } catch (error) {
            result.errors.push(`Directory cleanup failed: ${error}`);
        }

        return result;
    }

    private async removeEmptyDirectories(dirPath: string): Promise<void> {
        try {
            const files = await readdir(dirPath);
            if (files.length === 0) {
                await rmdir(dirPath);
            }
        } catch (error) {
            // 忽略错误
        }
    }

    private clearFileCache(): void {
        this.fileCache.clear();
    }

    private async startWatching(): Promise<void> {
        // 文件监控实现
    }

    private async stopAllWatching(): Promise<void> {
        for (const [path, watcher] of this.watchers) {
            await watcher.watcher.close();
        }
        this.watchers.clear();
    }
}
