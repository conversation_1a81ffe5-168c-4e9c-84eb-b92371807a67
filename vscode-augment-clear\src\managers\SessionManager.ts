/**
 * Machine Code Reset Plugin Session Manager
 * 会话数据管理器
 */

import * as vscode from 'vscode';
import { SessionData } from '../types';
import { CryptoUtils } from '../utils/crypto';
import { Logger } from '../utils/logger';
import { STORAGE_KEYS } from '../utils/constants';

export class SessionManager {
    private context: vscode.ExtensionContext;
    private logger: Logger;
    private currentSession?: SessionData;

    constructor(context: vscode.ExtensionContext) {
        this.context = context;
        this.logger = Logger.getInstance(context);
    }

    /**
     * 获取当前会话数据
     */
    async getCurrentSession(): Promise<SessionData> {
        if (!this.currentSession) {
            this.currentSession = await this.loadOrCreateSession();
        }
        return this.currentSession;
    }

    /**
     * 重置会话数据
     */
    async resetSession(): Promise<void> {
        this.logger.info('Starting session reset');

        try {
            // 创建新的会话数据
            const newSession: SessionData = {
                sessionId: CryptoUtils.generateSessionId(),
                authTokens: [],
                userPreferences: {},
                recentFiles: [],
                workspaceHistory: []
            };

            // 清除所有会话相关的存储
            await this.clearSessionStorage();
            
            // 保存新的会话数据
            await this.saveSession(newSession);
            this.currentSession = newSession;

            this.logger.info('Session reset successfully', {
                sessionId: newSession.sessionId
            });

        } catch (error) {
            this.logger.error('Failed to reset session', error);
            throw error;
        }
    }

    /**
     * 清除会话存储
     */
    private async clearSessionStorage(): Promise<void> {
        try {
            // 清除全局状态中的会话相关数据
            const sessionKeys = [
                STORAGE_KEYS.SESSION_DATA,
                'recentFiles',
                'workspaceHistory',
                'userPreferences',
                'authTokens',
                'lastWorkspace',
                'openEditors',
                'searchHistory',
                'commandHistory'
            ];

            for (const key of sessionKeys) {
                await this.context.globalState.update(key, undefined);
            }

            // 清除工作空间状态
            if (this.context.workspaceState) {
                const workspaceKeys = await this.context.workspaceState.keys();
                for (const key of workspaceKeys) {
                    await this.context.workspaceState.update(key, undefined);
                }
            }

            // 清除密钥存储
            await this.clearSecrets();

            this.logger.debug('Session storage cleared');

        } catch (error) {
            this.logger.error('Failed to clear session storage', error);
            throw error;
        }
    }

    /**
     * 清除密钥存储
     */
    private async clearSecrets(): Promise<void> {
        try {
            const secretKeys = [
                'authToken',
                'refreshToken',
                'apiKey',
                'sessionToken',
                'userCredentials',
                'oauthToken'
            ];

            for (const key of secretKeys) {
                try {
                    await this.context.secrets.delete(key);
                } catch (error) {
                    // 忽略不存在的密钥错误
                    this.logger.debug(`Secret key ${key} not found or already deleted`);
                }
            }

            this.logger.debug('Secrets cleared');

        } catch (error) {
            this.logger.warn('Failed to clear some secrets', error);
        }
    }

    /**
     * 加载或创建会话
     */
    private async loadOrCreateSession(): Promise<SessionData> {
        try {
            const stored = this.context.globalState.get<SessionData>(STORAGE_KEYS.SESSION_DATA);
            
            if (stored && this.isValidSessionData(stored)) {
                this.logger.debug('Loaded existing session data');
                return stored;
            }
        } catch (error) {
            this.logger.warn('Failed to load stored session data', error);
        }

        // 创建新的会话数据
        this.logger.info('Creating new session data');
        const sessionData: SessionData = {
            sessionId: CryptoUtils.generateSessionId(),
            authTokens: [],
            userPreferences: {},
            recentFiles: [],
            workspaceHistory: []
        };

        await this.saveSession(sessionData);
        return sessionData;
    }

    /**
     * 保存会话数据
     */
    private async saveSession(sessionData: SessionData): Promise<void> {
        try {
            await this.context.globalState.update(STORAGE_KEYS.SESSION_DATA, sessionData);
            this.logger.debug('Session data saved successfully');
        } catch (error) {
            this.logger.error('Failed to save session data', error);
            throw error;
        }
    }

    /**
     * 验证会话数据
     */
    private isValidSessionData(sessionData: any): sessionData is SessionData {
        return sessionData &&
               typeof sessionData.sessionId === 'string' &&
               Array.isArray(sessionData.authTokens) &&
               typeof sessionData.userPreferences === 'object' &&
               Array.isArray(sessionData.recentFiles) &&
               Array.isArray(sessionData.workspaceHistory);
    }

    /**
     * 清除所有认证令牌
     */
    async clearAuthTokens(): Promise<void> {
        const session = await this.getCurrentSession();
        session.authTokens = [];
        await this.saveSession(session);
        this.logger.info('All auth tokens cleared');
    }

    /**
     * 清除用户偏好
     */
    async clearUserPreferences(): Promise<void> {
        const session = await this.getCurrentSession();
        session.userPreferences = {};
        await this.saveSession(session);
        this.logger.info('User preferences cleared');
    }

    /**
     * 清除最近文件
     */
    async clearRecentFiles(): Promise<void> {
        const session = await this.getCurrentSession();
        session.recentFiles = [];
        await this.saveSession(session);
        this.logger.info('Recent files cleared');
    }

    /**
     * 清除工作空间历史
     */
    async clearWorkspaceHistory(): Promise<void> {
        const session = await this.getCurrentSession();
        session.workspaceHistory = [];
        await this.saveSession(session);
        this.logger.info('Workspace history cleared');
    }

    /**
     * 获取会话统计信息
     */
    async getSessionStatistics(): Promise<any> {
        const session = await this.getCurrentSession();
        
        return {
            sessionId: session.sessionId,
            authTokensCount: session.authTokens.length,
            userPreferencesCount: Object.keys(session.userPreferences).length,
            recentFilesCount: session.recentFiles.length,
            workspaceHistoryCount: session.workspaceHistory.length,
            globalStateKeys: this.context.globalState.keys().length,
            workspaceStateKeys: this.context.workspaceState?.keys().length || 0
        };
    }
}
