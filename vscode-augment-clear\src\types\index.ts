/**
 * Machine Code Reset Plugin Types
 * 完整的类型定义文件
 */

export interface DeviceInfo {
    deviceId: string;
    machineId: string;
    sessionId: string;
    hardwareFingerprint: string;
    createdAt: Date;
    lastResetAt?: Date;
}

export interface ResetOptions {
    resetDevice: boolean;
    resetSession: boolean;
    resetStorage: boolean;
    resetConfig: boolean;
    resetCache: boolean;
    createBackup: boolean;
    secureDelete: boolean;
}

export interface BackupInfo {
    id: string;
    name: string;
    createdAt: Date;
    size: number;
    deviceInfo: DeviceInfo;
    dataTypes: string[];
    filePath: string;
}

export interface ResetResult {
    success: boolean;
    message: string;
    details: {
        deviceReset: boolean;
        sessionReset: boolean;
        storageReset: boolean;
        configReset: boolean;
        cacheReset: boolean;
        backupCreated: boolean;
    };
    errors: string[];
    warnings: string[];
    duration: number;
}

export interface StorageData {
    globalState: Record<string, any>;
    workspaceState: Record<string, any>;
    secrets: Record<string, string>;
    memento: Record<string, any>;
}

export interface CacheInfo {
    path: string;
    size: number;
    type: 'file' | 'directory';
    lastModified: Date;
}

export interface SessionData {
    sessionId: string;
    authTokens: string[];
    userPreferences: Record<string, any>;
    recentFiles: string[];
    workspaceHistory: string[];
}

export interface ConfigData {
    userSettings: Record<string, any>;
    workspaceSettings: Record<string, any>;
    extensions: string[];
    keybindings: any[];
}

export interface ResetProgress {
    stage: string;
    progress: number;
    message: string;
    currentOperation: string;
}

export interface LogEntry {
    timestamp: Date;
    level: 'debug' | 'info' | 'warn' | 'error';
    message: string;
    data?: any;
}

export interface SecurityOptions {
    overwritePasses: number;
    useRandomData: boolean;
    verifyDeletion: boolean;
    encryptBackups: boolean;
}

export interface HardwareInfo {
    cpuId: string;
    motherboardId: string;
    diskId: string;
    networkMac: string;
    biosId: string;
    systemUuid: string;
}

export interface ResetStatistics {
    totalResets: number;
    lastResetDate: Date;
    averageResetTime: number;
    successRate: number;
    dataCleared: number;
    backupsCreated: number;
}

export type ResetStage = 
    | 'initializing'
    | 'creating_backup'
    | 'resetting_device'
    | 'resetting_session'
    | 'clearing_storage'
    | 'clearing_cache'
    | 'resetting_config'
    | 'verifying'
    | 'completed'
    | 'failed';

export type LogLevel = 'debug' | 'info' | 'warn' | 'error';

export type DataType = 
    | 'device'
    | 'session'
    | 'storage'
    | 'cache'
    | 'config'
    | 'secrets'
    | 'preferences';

export interface ResetEvent {
    type: 'start' | 'progress' | 'complete' | 'error';
    stage: ResetStage;
    data?: any;
    timestamp: Date;
}

export interface PluginConfig {
    autoBackup: boolean;
    confirmReset: boolean;
    logLevel: LogLevel;
    secureDelete: boolean;
    backupRetention: number;
    maxBackupSize: number;
    encryptBackups: boolean;
    overwritePasses: number;
}

export interface ValidationResult {
    isValid: boolean;
    errors: string[];
    warnings: string[];
}

export interface CleanupTarget {
    path: string;
    type: DataType;
    size: number;
    priority: number;
    secure: boolean;
}

export interface ResetContext {
    workspaceRoot?: string;
    extensionPath: string;
    globalStoragePath: string;
    workspaceStoragePath?: string;
    logPath: string;
    backupPath: string;
}
