/**
 * Machine Code Reset Plugin Logger
 * 日志记录工具
 */

import * as vscode from 'vscode';
import * as fs from 'fs';
import * as path from 'path';
import { LogEntry, LogLevel } from '../types';
import { LOG_LEVELS, PATHS } from './constants';

export class Logger {
    private static instance: Logger;
    private logLevel: LogLevel = 'info';
    private logPath: string;
    private outputChannel: vscode.OutputChannel;
    private logEntries: LogEntry[] = [];
    private maxLogEntries: number = 1000;

    private constructor(context: vscode.ExtensionContext) {
        this.logPath = path.join(context.globalStorageUri?.fsPath || '', PATHS.LOG_DIR);
        this.outputChannel = vscode.window.createOutputChannel('Machine Code Reset');
        this.ensureLogDirectory();
        this.loadConfig();
    }

    /**
     * 获取Logger单例
     */
    static getInstance(context?: vscode.ExtensionContext): Logger {
        if (!Logger.instance && context) {
            Logger.instance = new Logger(context);
        }
        return Logger.instance;
    }

    /**
     * 设置日志级别
     */
    setLogLevel(level: LogLevel): void {
        this.logLevel = level;
    }

    /**
     * 获取当前日志级别
     */
    getLogLevel(): LogLevel {
        return this.logLevel;
    }

    /**
     * 记录调试信息
     */
    debug(message: string, data?: any): void {
        this.log('debug', message, data);
    }

    /**
     * 记录信息
     */
    info(message: string, data?: any): void {
        this.log('info', message, data);
    }

    /**
     * 记录警告
     */
    warn(message: string, data?: any): void {
        this.log('warn', message, data);
    }

    /**
     * 记录错误
     */
    error(message: string, data?: any): void {
        this.log('error', message, data);
    }

    /**
     * 核心日志记录方法
     */
    private log(level: LogLevel, message: string, data?: any): void {
        if (!this.shouldLog(level)) {
            return;
        }

        const logEntry: LogEntry = {
            timestamp: new Date(),
            level,
            message,
            data
        };

        this.logEntries.push(logEntry);
        this.trimLogEntries();

        // 输出到VSCode输出面板
        this.outputToChannel(logEntry);

        // 写入日志文件
        this.writeToFile(logEntry);
    }

    /**
     * 判断是否应该记录该级别的日志
     */
    private shouldLog(level: LogLevel): boolean {
        const levels = ['debug', 'info', 'warn', 'error'];
        const currentLevelIndex = levels.indexOf(this.logLevel);
        const messageLevelIndex = levels.indexOf(level);
        return messageLevelIndex >= currentLevelIndex;
    }

    /**
     * 输出到VSCode输出面板
     */
    private outputToChannel(entry: LogEntry): void {
        const timestamp = entry.timestamp.toISOString();
        const level = entry.level.toUpperCase().padEnd(5);
        const message = entry.message;
        const data = entry.data ? ` | Data: ${JSON.stringify(entry.data)}` : '';
        
        const logLine = `[${timestamp}] ${level} ${message}${data}`;
        this.outputChannel.appendLine(logLine);
    }

    /**
     * 写入日志文件
     */
    private async writeToFile(entry: LogEntry): Promise<void> {
        try {
            const logFileName = `machine-reset-${this.getDateString()}.log`;
            const logFilePath = path.join(this.logPath, logFileName);
            
            const timestamp = entry.timestamp.toISOString();
            const level = entry.level.toUpperCase().padEnd(5);
            const message = entry.message;
            const data = entry.data ? ` | Data: ${JSON.stringify(entry.data, null, 2)}` : '';
            
            const logLine = `[${timestamp}] ${level} ${message}${data}\n`;
            
            await fs.promises.appendFile(logFilePath, logLine, 'utf8');
        } catch (error) {
            console.error('Failed to write to log file:', error);
        }
    }

    /**
     * 获取日期字符串
     */
    private getDateString(): string {
        const now = new Date();
        return now.toISOString().split('T')[0];
    }

    /**
     * 确保日志目录存在
     */
    private ensureLogDirectory(): void {
        try {
            if (!fs.existsSync(this.logPath)) {
                fs.mkdirSync(this.logPath, { recursive: true });
            }
        } catch (error) {
            console.error('Failed to create log directory:', error);
        }
    }

    /**
     * 限制内存中的日志条目数量
     */
    private trimLogEntries(): void {
        if (this.logEntries.length > this.maxLogEntries) {
            this.logEntries = this.logEntries.slice(-this.maxLogEntries);
        }
    }

    /**
     * 加载配置
     */
    private loadConfig(): void {
        const config = vscode.workspace.getConfiguration('machineCodeReset');
        this.logLevel = config.get('logLevel', 'info') as LogLevel;
    }

    /**
     * 获取所有日志条目
     */
    getLogEntries(): LogEntry[] {
        return [...this.logEntries];
    }

    /**
     * 获取指定级别的日志条目
     */
    getLogEntriesByLevel(level: LogLevel): LogEntry[] {
        return this.logEntries.filter(entry => entry.level === level);
    }

    /**
     * 清除内存中的日志条目
     */
    clearLogEntries(): void {
        this.logEntries = [];
    }

    /**
     * 显示输出面板
     */
    show(): void {
        this.outputChannel.show();
    }

    /**
     * 隐藏输出面板
     */
    hide(): void {
        this.outputChannel.hide();
    }

    /**
     * 清除输出面板内容
     */
    clear(): void {
        this.outputChannel.clear();
    }

    /**
     * 导出日志到文件
     */
    async exportLogs(filePath: string): Promise<void> {
        try {
            const logs = this.logEntries.map(entry => {
                const timestamp = entry.timestamp.toISOString();
                const level = entry.level.toUpperCase().padEnd(5);
                const message = entry.message;
                const data = entry.data ? ` | Data: ${JSON.stringify(entry.data, null, 2)}` : '';
                return `[${timestamp}] ${level} ${message}${data}`;
            }).join('\n');

            await fs.promises.writeFile(filePath, logs, 'utf8');
            this.info(`Logs exported to: ${filePath}`);
        } catch (error) {
            this.error('Failed to export logs', error);
            throw error;
        }
    }

    /**
     * 获取日志统计信息
     */
    getLogStatistics(): { [key in LogLevel]: number } {
        const stats = {
            debug: 0,
            info: 0,
            warn: 0,
            error: 0
        };

        this.logEntries.forEach(entry => {
            stats[entry.level]++;
        });

        return stats;
    }

    /**
     * 清理旧日志文件
     */
    async cleanupOldLogs(daysToKeep: number = 7): Promise<void> {
        try {
            const files = await fs.promises.readdir(this.logPath);
            const cutoffDate = new Date();
            cutoffDate.setDate(cutoffDate.getDate() - daysToKeep);

            for (const file of files) {
                if (file.endsWith('.log')) {
                    const filePath = path.join(this.logPath, file);
                    const stats = await fs.promises.stat(filePath);
                    
                    if (stats.mtime < cutoffDate) {
                        await fs.promises.unlink(filePath);
                        this.info(`Deleted old log file: ${file}`);
                    }
                }
            }
        } catch (error) {
            this.error('Failed to cleanup old logs', error);
        }
    }

    /**
     * 销毁Logger实例
     */
    dispose(): void {
        this.outputChannel.dispose();
        this.clearLogEntries();
    }
}
