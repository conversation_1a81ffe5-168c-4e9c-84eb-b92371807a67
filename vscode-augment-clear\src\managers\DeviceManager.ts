/**
 * Machine Code Reset Plugin Device Manager
 * 设备标识符管理器
 */

import * as vscode from 'vscode';
import * as os from 'os';
import * as crypto from 'crypto';
import { exec } from 'child_process';
import { promisify } from 'util';
import { DeviceInfo, HardwareInfo } from '../types';
import { CryptoUtils } from '../utils/crypto';
import { Logger } from '../utils/logger';
import { STORAGE_KEYS, HARDWARE_COMMANDS, TIMEOUTS } from '../utils/constants';

const execAsync = promisify(exec);

export class DeviceManager {
    private context: vscode.ExtensionContext;
    private logger: Logger;
    private currentDeviceInfo?: DeviceInfo;

    constructor(context: vscode.ExtensionContext) {
        this.context = context;
        this.logger = Logger.getInstance(context);
    }

    /**
     * 获取当前设备信息
     */
    async getCurrentDeviceInfo(): Promise<DeviceInfo> {
        if (!this.currentDeviceInfo) {
            this.currentDeviceInfo = await this.loadOrCreateDeviceInfo();
        }
        return this.currentDeviceInfo;
    }

    /**
     * 重置设备标识符
     */
    async resetDeviceIdentifiers(): Promise<void> {
        this.logger.info('Starting device identifier reset');

        try {
            // 获取硬件信息
            const hardwareInfo = await this.getHardwareInfo();
            
            // 生成新的设备信息
            const newDeviceInfo: DeviceInfo = {
                deviceId: CryptoUtils.generateUUID(),
                machineId: CryptoUtils.generateMachineId(hardwareInfo),
                sessionId: CryptoUtils.generateSessionId(),
                hardwareFingerprint: CryptoUtils.generateDeviceFingerprint(hardwareInfo),
                createdAt: new Date(),
                lastResetAt: new Date()
            };

            // 保存新的设备信息
            await this.saveDeviceInfo(newDeviceInfo);
            this.currentDeviceInfo = newDeviceInfo;

            this.logger.info('Device identifiers reset successfully', {
                deviceId: newDeviceInfo.deviceId,
                machineId: newDeviceInfo.machineId,
                sessionId: newDeviceInfo.sessionId
            });

        } catch (error) {
            this.logger.error('Failed to reset device identifiers', error);
            throw error;
        }
    }

    /**
     * 获取硬件信息
     */
    async getHardwareInfo(): Promise<HardwareInfo> {
        this.logger.debug('Collecting hardware information');

        const platform = os.platform();
        const hardwareInfo: HardwareInfo = {
            cpuId: '',
            motherboardId: '',
            diskId: '',
            networkMac: '',
            biosId: '',
            systemUuid: ''
        };

        try {
            // 并行获取硬件信息
            const promises = [
                this.getHardwareValue('CPU_ID', platform),
                this.getHardwareValue('MOTHERBOARD_ID', platform),
                this.getHardwareValue('DISK_ID', platform),
                this.getHardwareValue('NETWORK_MAC', platform),
                this.getHardwareValue('BIOS_ID', platform),
                this.getHardwareValue('SYSTEM_UUID', platform)
            ];

            const results = await Promise.allSettled(promises);
            
            hardwareInfo.cpuId = this.getResultValue(results[0]) || this.generateFallbackId('CPU');
            hardwareInfo.motherboardId = this.getResultValue(results[1]) || this.generateFallbackId('MB');
            hardwareInfo.diskId = this.getResultValue(results[2]) || this.generateFallbackId('DISK');
            hardwareInfo.networkMac = this.getResultValue(results[3]) || this.generateFallbackId('MAC');
            hardwareInfo.biosId = this.getResultValue(results[4]) || this.generateFallbackId('BIOS');
            hardwareInfo.systemUuid = this.getResultValue(results[5]) || this.generateFallbackId('SYS');

            this.logger.debug('Hardware information collected', hardwareInfo);
            return hardwareInfo;

        } catch (error) {
            this.logger.warn('Failed to collect some hardware information, using fallback values', error);
            
            // 使用备用方法生成硬件信息
            return this.generateFallbackHardwareInfo();
        }
    }

    /**
     * 获取硬件值
     */
    private async getHardwareValue(type: keyof typeof HARDWARE_COMMANDS, platform: string): Promise<string> {
        const commands = HARDWARE_COMMANDS[type];
        const command = commands[platform as keyof typeof commands];

        if (!command) {
            throw new Error(`No command available for ${type} on ${platform}`);
        }

        try {
            const { stdout } = await execAsync(command, { timeout: TIMEOUTS.HARDWARE_SCAN });
            return this.parseHardwareOutput(stdout.trim(), type);
        } catch (error) {
            this.logger.warn(`Failed to get ${type}`, error);
            throw error;
        }
    }

    /**
     * 解析硬件输出
     */
    private parseHardwareOutput(output: string, type: string): string {
        if (!output) {
            return '';
        }

        // 根据不同的硬件类型解析输出
        switch (type) {
            case 'CPU_ID':
                return this.extractValue(output, /ProcessorId=(.+)/i) || output.split('\n')[0];
            
            case 'MOTHERBOARD_ID':
                return this.extractValue(output, /SerialNumber=(.+)/i) || output.split('\n')[0];
            
            case 'DISK_ID':
                return this.extractValue(output, /SerialNumber=(.+)/i) || output.split('\n')[0];
            
            case 'NETWORK_MAC':
                const macMatch = output.match(/([0-9A-Fa-f]{2}[:-]){5}([0-9A-Fa-f]{2})/);
                return macMatch ? macMatch[0] : '';
            
            case 'BIOS_ID':
                return this.extractValue(output, /SerialNumber=(.+)/i) || output.split('\n')[0];
            
            case 'SYSTEM_UUID':
                return this.extractValue(output, /UUID=(.+)/i) || output.split('\n')[0];
            
            default:
                return output.split('\n')[0];
        }
    }

    /**
     * 提取值
     */
    private extractValue(text: string, regex: RegExp): string {
        const match = text.match(regex);
        return match ? match[1].trim() : '';
    }

    /**
     * 获取结果值
     */
    private getResultValue(result: PromiseSettledResult<string>): string {
        return result.status === 'fulfilled' ? result.value : '';
    }

    /**
     * 生成备用ID
     */
    private generateFallbackId(prefix: string): string {
        const timestamp = Date.now().toString();
        const random = CryptoUtils.generateRandomString(8);
        const hostname = os.hostname();
        const combined = `${prefix}-${hostname}-${timestamp}-${random}`;
        return CryptoUtils.createHash(combined).substring(0, 16).toUpperCase();
    }

    /**
     * 生成备用硬件信息
     */
    private generateFallbackHardwareInfo(): HardwareInfo {
        const baseInfo = {
            hostname: os.hostname(),
            platform: os.platform(),
            arch: os.arch(),
            release: os.release(),
            cpus: os.cpus().length.toString(),
            memory: os.totalmem().toString()
        };

        return {
            cpuId: this.generateFallbackId('CPU'),
            motherboardId: this.generateFallbackId('MB'),
            diskId: this.generateFallbackId('DISK'),
            networkMac: this.generateFallbackId('MAC'),
            biosId: this.generateFallbackId('BIOS'),
            systemUuid: CryptoUtils.generateUUID()
        };
    }

    /**
     * 加载或创建设备信息
     */
    private async loadOrCreateDeviceInfo(): Promise<DeviceInfo> {
        try {
            const stored = this.context.globalState.get<DeviceInfo>(STORAGE_KEYS.DEVICE_INFO);
            
            if (stored && this.isValidDeviceInfo(stored)) {
                this.logger.debug('Loaded existing device info');
                return stored;
            }
        } catch (error) {
            this.logger.warn('Failed to load stored device info', error);
        }

        // 创建新的设备信息
        this.logger.info('Creating new device info');
        const hardwareInfo = await this.getHardwareInfo();
        
        const deviceInfo: DeviceInfo = {
            deviceId: CryptoUtils.generateUUID(),
            machineId: CryptoUtils.generateMachineId(hardwareInfo),
            sessionId: CryptoUtils.generateSessionId(),
            hardwareFingerprint: CryptoUtils.generateDeviceFingerprint(hardwareInfo),
            createdAt: new Date()
        };

        await this.saveDeviceInfo(deviceInfo);
        return deviceInfo;
    }

    /**
     * 保存设备信息
     */
    private async saveDeviceInfo(deviceInfo: DeviceInfo): Promise<void> {
        try {
            await this.context.globalState.update(STORAGE_KEYS.DEVICE_INFO, deviceInfo);
            this.logger.debug('Device info saved successfully');
        } catch (error) {
            this.logger.error('Failed to save device info', error);
            throw error;
        }
    }

    /**
     * 验证设备信息
     */
    private isValidDeviceInfo(deviceInfo: any): deviceInfo is DeviceInfo {
        return deviceInfo &&
               typeof deviceInfo.deviceId === 'string' &&
               typeof deviceInfo.machineId === 'string' &&
               typeof deviceInfo.sessionId === 'string' &&
               typeof deviceInfo.hardwareFingerprint === 'string' &&
               deviceInfo.createdAt instanceof Date;
    }

    /**
     * 获取设备统计信息
     */
    async getDeviceStatistics(): Promise<any> {
        const deviceInfo = await this.getCurrentDeviceInfo();
        const hardwareInfo = await this.getHardwareInfo();
        
        return {
            deviceInfo,
            hardwareInfo,
            systemInfo: {
                platform: os.platform(),
                arch: os.arch(),
                release: os.release(),
                hostname: os.hostname(),
                uptime: os.uptime(),
                memory: {
                    total: os.totalmem(),
                    free: os.freemem()
                },
                cpus: os.cpus().length
            }
        };
    }

    /**
     * 清理设备数据
     */
    async clearDeviceData(): Promise<void> {
        try {
            await this.context.globalState.update(STORAGE_KEYS.DEVICE_INFO, undefined);
            this.currentDeviceInfo = undefined;
            this.logger.info('Device data cleared successfully');
        } catch (error) {
            this.logger.error('Failed to clear device data', error);
            throw error;
        }
    }
}
